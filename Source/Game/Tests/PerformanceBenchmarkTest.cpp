// Copyright Epic Games, Inc. All Rights Reserved.

#include "CoreMinimal.h"
#include "Misc/AutomationTest.h"
#include "Performance/PerformanceOptimizer.h"
#include "Performance/CacheManager.h"
#include "Performance/ObjectPoolManager.h"
#include "Performance/GPUComputeManager.h"
#include "LevelGen/MapUtil.h"
#include "Engine/World.h"
#include "Tests/AutomationCommon.h"
#include "HAL/PlatformFilemanager.h"

#if WITH_DEV_AUTOMATION_TESTS

/**
 * 【性能基准测试基类】
 * 提供性能测试的通用功能和工具
 */
class FPerformanceBenchmarkBase
{
public:
    /**
     * 测量函数执行时间
     * @param TestFunction 要测量的函数
     * @return 执行时间（毫秒）
     */
    template<typename FunctionType>
    static double MeasureExecutionTime(FunctionType TestFunction)
    {
        double StartTime = FPlatformTime::Seconds();
        TestFunction();
        double EndTime = FPlatformTime::Seconds();
        return (EndTime - StartTime) * 1000.0; // 转换为毫秒
    }

    /**
     * 创建大量测试数据
     * @param Count 数据数量
     * @return 测试数据数组
     */
    static TArray<FMapCell> CreateLargeTestDataset(int32 Count)
    {
        TArray<FMapCell> TestData;
        TestData.Reserve(Count);
        
        for (int32 i = 0; i < Count; ++i)
        {
            FMapCell Cell;
            Cell.SurfaceCoverType = static_cast<ESurfaceCoverType>(i % 6);
            Cell.ZLevel = FMath::FRandRange(0.0f, 100.0f);
            Cell.Temperature = FMath::FRandRange(-10.0f, 40.0f);
            TestData.Add(Cell);
        }
        
        return TestData;
    }

    /**
     * 创建重复性测试数据（用于压缩测试）
     * @param Count 数据数量
     * @return 重复性测试数据
     */
    static TArray<FMapCell> CreateRepetitiveTestDataset(int32 Count)
    {
        TArray<FMapCell> TestData;
        TestData.Reserve(Count);
        
        // 创建几种基础模式
        TArray<FMapCell> Patterns;
        for (int32 i = 0; i < 5; ++i)
        {
            FMapCell Pattern;
            Pattern.SurfaceCoverType = static_cast<ESurfaceCoverType>(i);
            Pattern.ZLevel = static_cast<float>(i * 10);
            Pattern.Temperature = 20.0f;
            Patterns.Add(Pattern);
        }
        
        // 重复模式创建数据
        for (int32 i = 0; i < Count; ++i)
        {
            TestData.Add(Patterns[i % Patterns.Num()]);
        }
        
        return TestData;
    }
};

/**
 * 【对象池性能基准测试】
 * 对比使用对象池和直接创建对象的性能差异
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FObjectPoolPerformanceBenchmark,
    "Game.Performance.Benchmark.ObjectPoolPerformance",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FObjectPoolPerformanceBenchmark::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UPerformanceOptimizer* Optimizer = NewObject<UPerformanceOptimizer>(TestWorld);
    
    FPerformanceOptimizationParams TestParams;
    TestParams.bEnableObjectPooling = true;
    Optimizer->Initialize(TestParams);

    const int32 TestIterations = 10000;
    
    // 基准测试：直接创建对象
    double DirectCreationTime = FPerformanceBenchmarkBase::MeasureExecutionTime([&]()
    {
        TArray<TSharedPtr<FMapCell>> DirectObjects;
        DirectObjects.Reserve(TestIterations);
        
        for (int32 i = 0; i < TestIterations; ++i)
        {
            TSharedPtr<FMapCell> Cell = MakeShared<FMapCell>();
            Cell->SurfaceCoverType = ESurfaceCoverType::Grass;
            Cell->ZLevel = static_cast<float>(i);
            DirectObjects.Add(Cell);
        }
        
        // 清理
        DirectObjects.Empty();
    });

    // 基准测试：使用对象池
    double PooledCreationTime = FPerformanceBenchmarkBase::MeasureExecutionTime([&]()
    {
        TArray<TSharedPtr<FMapCell>> PooledObjects;
        PooledObjects.Reserve(TestIterations);
        
        for (int32 i = 0; i < TestIterations; ++i)
        {
            TSharedPtr<FMapCell> Cell = Optimizer->GetPooledMapCell();
            Cell->SurfaceCoverType = ESurfaceCoverType::Grass;
            Cell->ZLevel = static_cast<float>(i);
            PooledObjects.Add(Cell);
        }
        
        // 归还到池中
        for (auto& Cell : PooledObjects)
        {
            Optimizer->ReturnPooledMapCell(Cell);
        }
    });

    // 计算性能提升
    double PerformanceImprovement = (DirectCreationTime - PooledCreationTime) / DirectCreationTime * 100.0;
    
    UE_LOG(LogTemp, Log, TEXT("Object Pool Performance Benchmark:"));
    UE_LOG(LogTemp, Log, TEXT("Direct Creation: %.2f ms"), DirectCreationTime);
    UE_LOG(LogTemp, Log, TEXT("Pooled Creation: %.2f ms"), PooledCreationTime);
    UE_LOG(LogTemp, Log, TEXT("Performance Improvement: %.2f%%"), PerformanceImprovement);

    // 验证对象池确实提供了性能提升（至少5%）
    TestTrue(TEXT("Object pool should provide performance improvement"), PerformanceImprovement > 5.0);

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【缓存系统性能基准测试】
 * 测试不同缓存级别的性能差异
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FCachePerformanceBenchmark,
    "Game.Performance.Benchmark.CachePerformance",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FCachePerformanceBenchmark::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UCacheManager* CacheManager = UCacheManager::Get(TestWorld);
    
    if (!CacheManager)
    {
        AddError(TEXT("Failed to get CacheManager instance"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // 创建测试数据
    const int32 DataSize = 1000;
    TArray<FMapCell> TestData = FPerformanceBenchmarkBase::CreateLargeTestDataset(DataSize);
    
    // 基准测试：L1内存缓存写入性能
    double L1WriteTime = FPerformanceBenchmarkBase::MeasureExecutionTime([&]()
    {
        for (int32 i = 0; i < 100; ++i)
        {
            FString Key = FString::Printf(TEXT("L1_Bench_%d"), i);
            CacheManager->CacheData(Key, TestData, ECacheLevel::L1_Memory);
        }
    });

    // 基准测试：L2压缩缓存写入性能
    double L2WriteTime = FPerformanceBenchmarkBase::MeasureExecutionTime([&]()
    {
        for (int32 i = 0; i < 100; ++i)
        {
            FString Key = FString::Printf(TEXT("L2_Bench_%d"), i);
            CacheManager->CacheData(Key, TestData, ECacheLevel::L2_Compressed);
        }
    });

    // 基准测试：L3磁盘缓存写入性能
    double L3WriteTime = FPerformanceBenchmarkBase::MeasureExecutionTime([&]()
    {
        for (int32 i = 0; i < 100; ++i)
        {
            FString Key = FString::Printf(TEXT("L3_Bench_%d"), i);
            CacheManager->CacheData(Key, TestData, ECacheLevel::L3_Disk);
        }
    });

    // 基准测试：缓存读取性能
    TArray<FMapCell> RetrievedData;
    double L1ReadTime = FPerformanceBenchmarkBase::MeasureExecutionTime([&]()
    {
        for (int32 i = 0; i < 100; ++i)
        {
            FString Key = FString::Printf(TEXT("L1_Bench_%d"), i);
            CacheManager->GetCachedData(Key, RetrievedData);
        }
    });

    double L2ReadTime = FPerformanceBenchmarkBase::MeasureExecutionTime([&]()
    {
        for (int32 i = 0; i < 100; ++i)
        {
            FString Key = FString::Printf(TEXT("L2_Bench_%d"), i);
            CacheManager->GetCachedData(Key, RetrievedData);
        }
    });

    double L3ReadTime = FPerformanceBenchmarkBase::MeasureExecutionTime([&]()
    {
        for (int32 i = 0; i < 100; ++i)
        {
            FString Key = FString::Printf(TEXT("L3_Bench_%d"), i);
            CacheManager->GetCachedData(Key, RetrievedData);
        }
    });

    // 输出基准测试结果
    UE_LOG(LogTemp, Log, TEXT("Cache Performance Benchmark:"));
    UE_LOG(LogTemp, Log, TEXT("L1 Write: %.2f ms, Read: %.2f ms"), L1WriteTime, L1ReadTime);
    UE_LOG(LogTemp, Log, TEXT("L2 Write: %.2f ms, Read: %.2f ms"), L2WriteTime, L2ReadTime);
    UE_LOG(LogTemp, Log, TEXT("L3 Write: %.2f ms, Read: %.2f ms"), L3WriteTime, L3ReadTime);

    // 验证性能层级关系（L1应该最快，L3最慢）
    TestTrue(TEXT("L1 read should be faster than L2"), L1ReadTime <= L2ReadTime * 1.1); // 允许10%误差
    TestTrue(TEXT("L2 read should be faster than L3"), L2ReadTime <= L3ReadTime * 1.1);

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【压缩算法性能基准测试】
 * 测试不同压缩算法的性能和压缩比
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FCompressionPerformanceBenchmark,
    "Game.Performance.Benchmark.CompressionPerformance",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FCompressionPerformanceBenchmark::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UCacheManager* CacheManager = UCacheManager::Get(TestWorld);
    
    if (!CacheManager)
    {
        AddError(TEXT("Failed to get CacheManager instance"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // 创建不同类型的测试数据
    const int32 DataSize = 5000;
    TArray<FMapCell> RandomData = FPerformanceBenchmarkBase::CreateLargeTestDataset(DataSize);
    TArray<FMapCell> RepetitiveData = FPerformanceBenchmarkBase::CreateRepetitiveTestDataset(DataSize);

    // 基准测试：随机数据压缩性能
    double RandomCompressionTime = FPerformanceBenchmarkBase::MeasureExecutionTime([&]()
    {
        for (int32 i = 0; i < 50; ++i)
        {
            FString Key = FString::Printf(TEXT("RandomComp_%d"), i);
            CacheManager->CacheData(Key, RandomData, ECacheLevel::L2_Compressed);
        }
    });

    // 基准测试：重复数据压缩性能
    double RepetitiveCompressionTime = FPerformanceBenchmarkBase::MeasureExecutionTime([&]()
    {
        for (int32 i = 0; i < 50; ++i)
        {
            FString Key = FString::Printf(TEXT("RepetitiveComp_%d"), i);
            CacheManager->CacheData(Key, RepetitiveData, ECacheLevel::L2_Compressed);
        }
    });

    // 基准测试：解压缩性能
    TArray<FMapCell> DecompressedData;
    double RandomDecompressionTime = FPerformanceBenchmarkBase::MeasureExecutionTime([&]()
    {
        for (int32 i = 0; i < 50; ++i)
        {
            FString Key = FString::Printf(TEXT("RandomComp_%d"), i);
            CacheManager->GetCachedData(Key, DecompressedData);
        }
    });

    double RepetitiveDecompressionTime = FPerformanceBenchmarkBase::MeasureExecutionTime([&]()
    {
        for (int32 i = 0; i < 50; ++i)
        {
            FString Key = FString::Printf(TEXT("RepetitiveComp_%d"), i);
            CacheManager->GetCachedData(Key, DecompressedData);
        }
    });

    // 输出基准测试结果
    UE_LOG(LogTemp, Log, TEXT("Compression Performance Benchmark:"));
    UE_LOG(LogTemp, Log, TEXT("Random Data - Compression: %.2f ms, Decompression: %.2f ms"), 
           RandomCompressionTime, RandomDecompressionTime);
    UE_LOG(LogTemp, Log, TEXT("Repetitive Data - Compression: %.2f ms, Decompression: %.2f ms"), 
           RepetitiveCompressionTime, RepetitiveDecompressionTime);

    // 验证压缩算法能够处理不同类型的数据
    TestTrue(TEXT("Random data compression should complete"), RandomCompressionTime > 0);
    TestTrue(TEXT("Repetitive data compression should complete"), RepetitiveCompressionTime > 0);
    TestTrue(TEXT("Decompression should work correctly"), DecompressedData.Num() > 0);

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【GPU计算性能基准测试】
 * 对比GPU和CPU计算的性能差异
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FGPUComputePerformanceBenchmark,
    "Game.Performance.Benchmark.GPUComputePerformance",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FGPUComputePerformanceBenchmark::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UGPUComputeManager* GPUManager = UGPUComputeManager::Get(TestWorld);
    
    if (!GPUManager)
    {
        AddError(TEXT("Failed to get GPUComputeManager instance"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // 创建大量测试位置数据
    const int32 PositionCount = 10000;
    TArray<FVector> TestPositions;
    TestPositions.Reserve(PositionCount);
    
    for (int32 i = 0; i < PositionCount; ++i)
    {
        TestPositions.Add(FVector(
            FMath::FRandRange(-1000.0f, 1000.0f),
            FMath::FRandRange(-1000.0f, 1000.0f),
            FMath::FRandRange(-100.0f, 100.0f)
        ));
    }

    FVector CenterPoint = FVector::ZeroVector;

    // 基准测试：CPU距离计算
    TArray<float> CPUResults;
    double CPUComputeTime = FPerformanceBenchmarkBase::MeasureExecutionTime([&]()
    {
        CPUResults.Empty();
        CPUResults.Reserve(PositionCount);
        
        for (const FVector& Position : TestPositions)
        {
            float Distance = FVector::Dist(Position, CenterPoint);
            CPUResults.Add(Distance);
        }
    });

    // 基准测试：GPU距离计算
    TArray<float> GPUResults;
    double GPUComputeTime = FPerformanceBenchmarkBase::MeasureExecutionTime([&]()
    {
        // 转换为FIntPoint数组
        TArray<FIntPoint> PointsA, PointsB;
        for (int32 i = 0; i < TestPositions.Num(); ++i)
        {
            PointsA.Add(FIntPoint(static_cast<int32>(CenterPoint.X), static_cast<int32>(CenterPoint.Y)));
            PointsB.Add(FIntPoint(static_cast<int32>(TestPositions[i].X), static_cast<int32>(TestPositions[i].Y)));
        }
        int32 TaskID = GPUManager->CalculateDistancesBatch_GPU(PointsA, PointsB, GPUResults);
        // 注意：实际实现中需要等待GPU计算完成
    });

    // 计算性能提升
    double PerformanceRatio = CPUComputeTime / GPUComputeTime;
    
    UE_LOG(LogTemp, Log, TEXT("GPU Compute Performance Benchmark:"));
    UE_LOG(LogTemp, Log, TEXT("CPU Compute Time: %.2f ms"), CPUComputeTime);
    UE_LOG(LogTemp, Log, TEXT("GPU Compute Time: %.2f ms"), GPUComputeTime);
    UE_LOG(LogTemp, Log, TEXT("GPU Speedup: %.2fx"), PerformanceRatio);

    // 验证结果数量正确
    TestEqual(TEXT("CPU results count should match input"), CPUResults.Num(), PositionCount);
    
    // 如果GPU计算可用，验证GPU结果
    if (GPUManager->IsGPUComputeAvailable() && GPUResults.Num() > 0)
    {
        TestEqual(TEXT("GPU results count should match input"), GPUResults.Num(), PositionCount);
        
        // 验证GPU和CPU结果的一致性（前几个结果）
        int32 CheckCount = FMath::Min(10, FMath::Min(CPUResults.Num(), GPUResults.Num()));
        for (int32 i = 0; i < CheckCount; ++i)
        {
            float Tolerance = 0.1f; // 允许小的浮点误差
            TestTrue(FString::Printf(TEXT("GPU result %d should match CPU result"), i),
                     FMath::IsNearlyEqual(GPUResults[i], CPUResults[i], Tolerance));
        }
    }

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【内存使用基准测试】
 * 测试优化前后的内存使用情况
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FMemoryUsageBenchmark,
    "Game.Performance.Benchmark.MemoryUsage",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FMemoryUsageBenchmark::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    
    // 记录初始内存使用
    FPlatformMemoryStats InitialMemStats = FPlatformMemory::GetStats();
    
    // 创建大量数据（不使用优化）
    const int32 ObjectCount = 5000;
    TArray<TSharedPtr<FMapCell>> DirectObjects;
    DirectObjects.Reserve(ObjectCount);
    
    for (int32 i = 0; i < ObjectCount; ++i)
    {
        TSharedPtr<FMapCell> Cell = MakeShared<FMapCell>();
        Cell->SurfaceCoverType = static_cast<ESurfaceCoverType>(i % 6);
        Cell->ZLevel = static_cast<float>(i);
        DirectObjects.Add(Cell);
    }
    
    // 记录直接创建后的内存使用
    FPlatformMemoryStats DirectMemStats = FPlatformMemory::GetStats();
    
    // 清理直接创建的对象
    DirectObjects.Empty();
    
    // 使用优化器创建相同数量的对象
    UPerformanceOptimizer* Optimizer = NewObject<UPerformanceOptimizer>(TestWorld);
    FPerformanceOptimizationParams TestParams;
    TestParams.bEnableObjectPooling = true;
    TestParams.bEnableCaching = true;
    Optimizer->Initialize(TestParams);
    
    TArray<TSharedPtr<FMapCell>> PooledObjects;
    PooledObjects.Reserve(ObjectCount);
    
    for (int32 i = 0; i < ObjectCount; ++i)
    {
        TSharedPtr<FMapCell> Cell = Optimizer->GetPooledMapCell();
        Cell->SurfaceCoverType = static_cast<ESurfaceCoverType>(i % 6);
        Cell->ZLevel = static_cast<float>(i);
        PooledObjects.Add(Cell);
    }
    
    // 记录使用对象池后的内存使用
    FPlatformMemoryStats PooledMemStats = FPlatformMemory::GetStats();
    
    // 归还对象到池中
    for (auto& Cell : PooledObjects)
    {
        Optimizer->ReturnPooledMapCell(Cell);
    }
    
    // 记录归还后的内存使用
    FPlatformMemoryStats FinalMemStats = FPlatformMemory::GetStats();
    
    // 计算内存使用差异
    SIZE_T DirectMemoryIncrease = DirectMemStats.UsedPhysical - InitialMemStats.UsedPhysical;
    SIZE_T PooledMemoryIncrease = PooledMemStats.UsedPhysical - InitialMemStats.UsedPhysical;
    SIZE_T FinalMemoryUsage = FinalMemStats.UsedPhysical - InitialMemStats.UsedPhysical;
    
    UE_LOG(LogTemp, Log, TEXT("Memory Usage Benchmark:"));
    UE_LOG(LogTemp, Log, TEXT("Direct Creation Memory Increase: %llu bytes"), DirectMemoryIncrease);
    UE_LOG(LogTemp, Log, TEXT("Pooled Creation Memory Increase: %llu bytes"), PooledMemoryIncrease);
    UE_LOG(LogTemp, Log, TEXT("Final Memory Usage: %llu bytes"), FinalMemoryUsage);
    
    // 验证对象池能够减少内存碎片（最终内存使用应该较低）
    TestTrue(TEXT("Memory usage should be reasonable"), FinalMemoryUsage >= 0);
    
    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

#endif // WITH_DEV_AUTOMATION_TESTS
