// Copyright Epic Games, Inc. All Rights Reserved.

#include "CoreMinimal.h"
#include "Misc/AutomationTest.h"
#include "Performance/CacheManager.h"
#include "LevelGen/MapUtil.h"
#include "Engine/World.h"
#include "Tests/AutomationCommon.h"
#include "HAL/PlatformFilemanager.h"

#if WITH_DEV_AUTOMATION_TESTS

/**
 * 【缓存管理器基础测试】
 * 测试缓存管理器的基本功能和初始化
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FCacheManagerBasicTest,
    "Game.Performance.CacheManager.BasicFunctionality",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FCacheManagerBasicTest::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    if (!TestWorld)
    {
        AddError(TEXT("Failed to create test world"));
        return false;
    }

    // 获取缓存管理器实例
    UCacheManager* CacheManager = UCacheManager::Get(TestWorld);
    if (!CacheManager)
    {
        AddError(TEXT("Failed to get CacheManager instance"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // 测试基本功能
    TestTrue(TEXT("Cache Manager should be valid"), IsValid(CacheManager));
    
    // 获取初始缓存统计
    FCacheStats InitialStats = CacheManager->GetCacheStats();
    TestTrue(TEXT("Initial cache stats should be valid"), InitialStats.L1Hits >= 0);

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【多级缓存测试】
 * 测试L1内存、L2压缩、L3磁盘缓存的功能
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FMultiLevelCacheTest,
    "Game.Performance.CacheManager.MultiLevelCache",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FMultiLevelCacheTest::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UCacheManager* CacheManager = UCacheManager::Get(TestWorld);
    
    if (!CacheManager)
    {
        AddError(TEXT("Failed to get CacheManager instance"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // 创建测试数据
    TArray<FMapCell> TestData;
    for (int32 i = 0; i < 20; ++i)
    {
        FMapCell Cell;
        Cell.SurfaceCoverType = ESurfaceCoverType::Grass;
        Cell.ZLevel = static_cast<float>(i);
        Cell.Temperature = 20.0f + i;
        TestData.Add(Cell);
    }

    // 测试L1内存缓存
    FString L1Key = TEXT("L1_TestChunk");
    CacheManager->CacheData(L1Key, TestData, ECacheLevel::L1_Memory);
    
    TArray<FMapCell> L1Retrieved;
    bool bL1Hit = CacheManager->GetCachedData(L1Key, L1Retrieved);
    TestTrue(TEXT("L1 cache should hit"), bL1Hit);
    TestEqual(TEXT("L1 data should match"), L1Retrieved.Num(), TestData.Num());

    // 测试L2压缩缓存
    FString L2Key = TEXT("L2_TestChunk");
    CacheManager->CacheData(L2Key, TestData, ECacheLevel::L2_Compressed);
    
    TArray<FMapCell> L2Retrieved;
    bool bL2Hit = CacheManager->GetCachedData(L2Key, L2Retrieved);
    TestTrue(TEXT("L2 cache should hit"), bL2Hit);
    TestEqual(TEXT("L2 data should match"), L2Retrieved.Num(), TestData.Num());

    // 测试L3磁盘缓存
    FString L3Key = TEXT("L3_TestChunk");
    CacheManager->CacheData(L3Key, TestData, ECacheLevel::L3_Disk);
    
    TArray<FMapCell> L3Retrieved;
    bool bL3Hit = CacheManager->GetCachedData(L3Key, L3Retrieved);
    TestTrue(TEXT("L3 cache should hit"), bL3Hit);
    TestEqual(TEXT("L3 data should match"), L3Retrieved.Num(), TestData.Num());

    // 验证数据完整性
    if (L1Retrieved.Num() == TestData.Num())
    {
        for (int32 i = 0; i < TestData.Num(); ++i)
        {
            TestEqual(TEXT("L1 cell height should match"), L1Retrieved[i].ZLevel, TestData[i].ZLevel);
            TestEqual(TEXT("L1 cell temperature should match"), L1Retrieved[i].Temperature, TestData[i].Temperature);
        }
    }

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【压缩算法测试】
 * 测试不同压缩算法的正确性和效率
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FCompressionAlgorithmTest,
    "Game.Performance.CacheManager.CompressionAlgorithm",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FCompressionAlgorithmTest::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UCacheManager* CacheManager = UCacheManager::Get(TestWorld);
    
    if (!CacheManager)
    {
        AddError(TEXT("Failed to get CacheManager instance"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // 创建高重复性数据（适合压缩）
    TArray<FMapCell> RepetitiveData;
    for (int32 i = 0; i < 100; ++i)
    {
        FMapCell Cell;
        Cell.SurfaceCoverType = ESurfaceCoverType::Grass; // 所有都是草地
        Cell.ZLevel = 1.0f; // 相同高度
        Cell.Temperature = 20.0f; // 相同温度
        RepetitiveData.Add(Cell);
    }

    // 测试高压缩比数据
    FString RepetitiveKey = TEXT("Repetitive_Data");
    CacheManager->CacheData(RepetitiveKey, RepetitiveData, ECacheLevel::L2_Compressed);
    
    TArray<FMapCell> RepetitiveRetrieved;
    bool bRepetitiveHit = CacheManager->GetCachedData(RepetitiveKey, RepetitiveRetrieved);
    TestTrue(TEXT("Repetitive data should be cached and retrieved"), bRepetitiveHit);
    TestEqual(TEXT("Repetitive data count should match"), RepetitiveRetrieved.Num(), RepetitiveData.Num());

    // 创建随机数据（压缩比较低）
    TArray<FMapCell> RandomData;
    for (int32 i = 0; i < 100; ++i)
    {
        FMapCell Cell;
        Cell.SurfaceCoverType = static_cast<ESurfaceCoverType>(FMath::RandRange(0, 5));
        Cell.ZLevel = FMath::FRandRange(0.0f, 100.0f);
        Cell.Temperature = FMath::FRandRange(-10.0f, 40.0f);
        RandomData.Add(Cell);
    }

    // 测试低压缩比数据
    FString RandomKey = TEXT("Random_Data");
    CacheManager->CacheData(RandomKey, RandomData, ECacheLevel::L2_Compressed);
    
    TArray<FMapCell> RandomRetrieved;
    bool bRandomHit = CacheManager->GetCachedData(RandomKey, RandomRetrieved);
    TestTrue(TEXT("Random data should be cached and retrieved"), bRandomHit);
    TestEqual(TEXT("Random data count should match"), RandomRetrieved.Num(), RandomData.Num());

    // 验证数据完整性
    if (RepetitiveRetrieved.Num() == RepetitiveData.Num())
    {
        for (int32 i = 0; i < RepetitiveData.Num(); ++i)
        {
            TestEqual(TEXT("Repetitive cell type should match"), 
                     static_cast<int32>(RepetitiveRetrieved[i].SurfaceCoverType), 
                     static_cast<int32>(RepetitiveData[i].SurfaceCoverType));
        }
    }

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【自适应压缩选择测试】
 * 测试系统根据数据特征自动选择最佳压缩算法
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FAdaptiveCompressionTest,
    "Game.Performance.CacheManager.AdaptiveCompression",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FAdaptiveCompressionTest::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UCacheManager* CacheManager = UCacheManager::Get(TestWorld);
    
    if (!CacheManager)
    {
        AddError(TEXT("Failed to get CacheManager instance"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // 测试不同类型的数据，验证自适应压缩选择
    
    // 1. 高重复性数据
    TArray<FMapCell> HighRepetitionData;
    for (int32 i = 0; i < 50; ++i)
    {
        FMapCell Cell;
        Cell.SurfaceCoverType = ESurfaceCoverType::Water;
        Cell.ZLevel = 0.0f;
        HighRepetitionData.Add(Cell);
    }

    // 2. 中等重复性数据
    TArray<FMapCell> MediumRepetitionData;
    for (int32 i = 0; i < 50; ++i)
    {
        FMapCell Cell;
        Cell.SurfaceCoverType = (i % 3 == 0) ? ESurfaceCoverType::Grass : ESurfaceCoverType::Rock;
        Cell.ZLevel = static_cast<float>(i % 5);
        MediumRepetitionData.Add(Cell);
    }

    // 3. 低重复性数据
    TArray<FMapCell> LowRepetitionData;
    for (int32 i = 0; i < 50; ++i)
    {
        FMapCell Cell;
        Cell.SurfaceCoverType = static_cast<ESurfaceCoverType>(FMath::RandRange(0, 5));
        Cell.ZLevel = FMath::FRandRange(0.0f, 100.0f);
        Cell.Temperature = FMath::FRandRange(-10.0f, 40.0f);
        LowRepetitionData.Add(Cell);
    }

    // 缓存所有数据类型
    CacheManager->CacheData(TEXT("HighRep"), HighRepetitionData, ECacheLevel::L2_Compressed);
    CacheManager->CacheData(TEXT("MediumRep"), MediumRepetitionData, ECacheLevel::L2_Compressed);
    CacheManager->CacheData(TEXT("LowRep"), LowRepetitionData, ECacheLevel::L2_Compressed);

    // 验证所有数据都能正确检索
    TArray<FMapCell> HighRepRetrieved, MediumRepRetrieved, LowRepRetrieved;
    
    bool bHighRepHit = CacheManager->GetCachedData(TEXT("HighRep"), HighRepRetrieved);
    bool bMediumRepHit = CacheManager->GetCachedData(TEXT("MediumRep"), MediumRepRetrieved);
    bool bLowRepHit = CacheManager->GetCachedData(TEXT("LowRep"), LowRepRetrieved);

    TestTrue(TEXT("High repetition data should be retrieved"), bHighRepHit);
    TestTrue(TEXT("Medium repetition data should be retrieved"), bMediumRepHit);
    TestTrue(TEXT("Low repetition data should be retrieved"), bLowRepHit);

    TestEqual(TEXT("High rep data count should match"), HighRepRetrieved.Num(), HighRepetitionData.Num());
    TestEqual(TEXT("Medium rep data count should match"), MediumRepRetrieved.Num(), MediumRepetitionData.Num());
    TestEqual(TEXT("Low rep data count should match"), LowRepRetrieved.Num(), LowRepetitionData.Num());

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【智能预测缓存测试】
 * 测试基于访问模式的智能预测缓存功能
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FPredictiveCacheTest,
    "Game.Performance.CacheManager.PredictiveCache",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FPredictiveCacheTest::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UCacheManager* CacheManager = UCacheManager::Get(TestWorld);
    
    if (!CacheManager)
    {
        AddError(TEXT("Failed to get CacheManager instance"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // 创建测试数据
    TArray<FMapCell> TestData;
    for (int32 i = 0; i < 10; ++i)
    {
        FMapCell Cell;
        Cell.SurfaceCoverType = ESurfaceCoverType::Grass;
        Cell.ZLevel = static_cast<float>(i);
        TestData.Add(Cell);
    }

    // 建立访问模式：A -> B -> C 的顺序访问
    FString KeyA = TEXT("ChunkA");
    FString KeyB = TEXT("ChunkB");
    FString KeyC = TEXT("ChunkC");

    // 缓存数据
    CacheManager->CacheData(KeyA, TestData, ECacheLevel::L1_Memory);
    CacheManager->CacheData(KeyB, TestData, ECacheLevel::L1_Memory);
    CacheManager->CacheData(KeyC, TestData, ECacheLevel::L1_Memory);

    // 模拟访问模式学习
    double CurrentTime = FPlatformTime::Seconds();
    CacheManager->LearnAccessPattern(KeyA, CurrentTime);
    CacheManager->LearnAccessPattern(KeyB, CurrentTime + 1.0);
    CacheManager->LearnAccessPattern(KeyC, CurrentTime + 2.0);

    // 重复模式几次以建立学习
    for (int32 i = 0; i < 3; ++i)
    {
        CurrentTime += 10.0;
        CacheManager->LearnAccessPattern(KeyA, CurrentTime);
        CacheManager->LearnAccessPattern(KeyB, CurrentTime + 1.0);
        CacheManager->LearnAccessPattern(KeyC, CurrentTime + 2.0);
    }

    // 测试预测功能
    TArray<FString> PredictedKeys = CacheManager->PredictNextAccess(KeyA);
    TestTrue(TEXT("Should predict next access keys"), PredictedKeys.Num() > 0);

    // 验证预测结果包含期望的键
    bool bFoundKeyB = PredictedKeys.Contains(KeyB);
    TestTrue(TEXT("Should predict KeyB after KeyA"), bFoundKeyB);

    // 测试预加载功能
    CacheManager->PreloadPredictedCache(PredictedKeys);
    
    // 验证预加载的数据仍然可以访问
    TArray<FMapCell> PreloadedData;
    bool bPreloadHit = CacheManager->GetCachedData(KeyB, PreloadedData);
    TestTrue(TEXT("Preloaded data should be accessible"), bPreloadHit);

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【缓存过期管理测试】
 * 测试缓存的自动过期和清理功能
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FCacheExpirationTest,
    "Game.Performance.CacheManager.CacheExpiration",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FCacheExpirationTest::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UCacheManager* CacheManager = UCacheManager::Get(TestWorld);
    
    if (!CacheManager)
    {
        AddError(TEXT("Failed to get CacheManager instance"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // 创建测试数据
    TArray<FMapCell> TestData;
    for (int32 i = 0; i < 5; ++i)
    {
        FMapCell Cell;
        Cell.SurfaceCoverType = ESurfaceCoverType::Grass;
        Cell.ZLevel = static_cast<float>(i);
        TestData.Add(Cell);
    }

    // 缓存数据
    FString TestKey = TEXT("ExpirationTest");
    CacheManager->CacheData(TestKey, TestData, ECacheLevel::L1_Memory);

    // 验证数据已缓存
    TArray<FMapCell> RetrievedData;
    bool bInitialHit = CacheManager->GetCachedData(TestKey, RetrievedData);
    TestTrue(TEXT("Data should be initially cached"), bInitialHit);

    // 获取初始缓存统计
    FCacheStats InitialStats = CacheManager->GetCacheStats();
    
    // 执行过期清理
    CacheManager->CleanupExpiredCache();
    
    // 获取清理后的统计
    FCacheStats CleanupStats = CacheManager->GetCacheStats();
    
    // 验证清理功能正常工作（统计信息应该有变化或保持合理）
    TestTrue(TEXT("Cache cleanup should complete without error"), true);
    TestTrue(TEXT("Cache stats should remain valid"), CleanupStats.L1Hits >= 0);

    // 测试强制清理所有缓存
    CacheManager->ClearAllCache();
    
    // 验证所有缓存已清理
    TArray<FMapCell> PostClearData;
    bool bPostClearHit = CacheManager->GetCachedData(TestKey, PostClearData);
    TestFalse(TEXT("Data should not be found after clear all"), bPostClearHit);

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【缓存统计测试】
 * 测试缓存统计信息的准确性和完整性
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FCacheStatsTest,
    "Game.Performance.CacheManager.CacheStats",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FCacheStatsTest::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UCacheManager* CacheManager = UCacheManager::Get(TestWorld);
    
    if (!CacheManager)
    {
        AddError(TEXT("Failed to get CacheManager instance"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // 获取初始统计
    FCacheStats InitialStats = CacheManager->GetCacheStats();
    
    // 创建和缓存测试数据
    TArray<FMapCell> TestData;
    for (int32 i = 0; i < 10; ++i)
    {
        FMapCell Cell;
        Cell.SurfaceCoverType = ESurfaceCoverType::Grass;
        Cell.ZLevel = static_cast<float>(i);
        TestData.Add(Cell);
    }

    // 执行多次缓存操作
    for (int32 i = 0; i < 5; ++i)
    {
        FString Key = FString::Printf(TEXT("StatsTest_%d"), i);
        CacheManager->CacheData(Key, TestData, ECacheLevel::L1_Memory);
    }

    // 执行多次检索操作
    TArray<FMapCell> RetrievedData;
    for (int32 i = 0; i < 5; ++i)
    {
        FString Key = FString::Printf(TEXT("StatsTest_%d"), i);
        CacheManager->GetCachedData(Key, RetrievedData); // 命中
    }

    // 执行一些未命中的检索
    for (int32 i = 10; i < 15; ++i)
    {
        FString Key = FString::Printf(TEXT("NonExistent_%d"), i);
        CacheManager->GetCachedData(Key, RetrievedData); // 未命中
    }

    // 获取最终统计
    FCacheStats FinalStats = CacheManager->GetCacheStats();
    
    // 验证统计信息的合理性
    TestTrue(TEXT("L1 hits should be non-negative"), FinalStats.L1Hits >= 0);
    TestTrue(TEXT("L2 hits should be non-negative"), FinalStats.L2Hits >= 0);
    TestTrue(TEXT("L3 hits should be non-negative"), FinalStats.L3Hits >= 0);
    TestTrue(TEXT("Misses should be non-negative"), FinalStats.Misses >= 0);
    TestTrue(TEXT("L1 memory usage should be non-negative"), FinalStats.L1MemoryUsage >= 0);

    // 验证统计信息有所变化
    TestTrue(TEXT("Statistics should have been updated"),
             (FinalStats.L1Hits + FinalStats.L2Hits + FinalStats.L3Hits) >=
             (InitialStats.L1Hits + InitialStats.L2Hits + InitialStats.L3Hits));

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

#endif // WITH_DEV_AUTOMATION_TESTS
