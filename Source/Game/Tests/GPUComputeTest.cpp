// Copyright Epic Games, Inc. All Rights Reserved.

#include "CoreMinimal.h"
#include "Misc/AutomationTest.h"
#include "Performance/GPUComputeManager.h"
#include "Performance/PerformanceOptimizer.h"
#include "LevelGen/MapUtil.h"
#include "Engine/World.h"
#include "Tests/AutomationCommon.h"
#include "RenderingThread.h"

#if WITH_DEV_AUTOMATION_TESTS

/**
 * 【GPU计算管理器基础测试】
 * 测试GPU计算管理器的基本功能和初始化
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FGPUComputeManagerBasicTest,
    "Game.Performance.GPUComputeManager.BasicFunctionality",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FGPUComputeManagerBasicTest::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    if (!TestWorld)
    {
        AddError(TEXT("Failed to create test world"));
        return false;
    }

    // 获取GPU计算管理器实例
    UGPUComputeManager* GPUManager = UGPUComputeManager::Get(TestWorld);
    if (!GPUManager)
    {
        AddError(TEXT("Failed to get GPUComputeManager instance"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // 测试基本功能
    TestTrue(TEXT("GPU Manager should be valid"), IsValid(GPUManager));
    
    // 测试GPU计算是否可用
    bool bGPUAvailable = GPUManager->IsGPUComputeAvailable();
    UE_LOG(LogTemp, Log, TEXT("GPU Compute Available: %s"), bGPUAvailable ? TEXT("Yes") : TEXT("No"));

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【批量距离计算测试】
 * 测试GPU批量距离计算的正确性和性能
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FBatchDistanceCalculationTest,
    "Game.Performance.GPUComputeManager.BatchDistanceCalculation",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FBatchDistanceCalculationTest::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UGPUComputeManager* GPUManager = UGPUComputeManager::Get(TestWorld);
    
    if (!GPUManager)
    {
        AddError(TEXT("Failed to get GPUComputeManager instance"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // 创建测试数据（使用FIntPoint类型）
    TArray<FIntPoint> PointsA;
    TArray<FIntPoint> PointsB;
    TArray<float> ExpectedDistances;

    // 生成测试位置对
    for (int32 i = 0; i < 10; ++i)
    {
        FIntPoint PointA(0, 0); // 起始点
        FIntPoint PointB(i * 10, 0); // 目标点沿X轴分布
        float ExpectedDistance = static_cast<float>(i * 10); // 预期距离

        PointsA.Add(PointA);
        PointsB.Add(PointB);
        ExpectedDistances.Add(ExpectedDistance);
    }

    // 执行批量距离计算
    TArray<float> CalculatedDistances;
    int32 TaskID = GPUManager->CalculateDistancesBatch_GPU(PointsA, PointsB, CalculatedDistances);
    
    // 验证任务ID有效
    TestTrue(TEXT("Task ID should be valid"), TaskID >= 0);

    // 等待计算完成（简单的同步等待）
    double StartTime = FPlatformTime::Seconds();
    bool bCompleted = false;
    while (!bCompleted && (FPlatformTime::Seconds() - StartTime) < 5.0) // 最多等待5秒
    {
        FPlatformProcess::Sleep(0.1f);
        // 检查任务是否完成（这里需要GPU管理器提供状态查询接口）
        bCompleted = true; // 暂时假设立即完成，实际应该查询任务状态
    }

    // 验证计算结果
    if (CalculatedDistances.Num() == ExpectedDistances.Num())
    {
        for (int32 i = 0; i < ExpectedDistances.Num(); ++i)
        {
            float Tolerance = 0.1f; // 允许小的浮点误差
            TestTrue(FString::Printf(TEXT("Distance %d should be approximately correct"), i),
                     FMath::IsNearlyEqual(CalculatedDistances[i], ExpectedDistances[i], Tolerance));
        }
    }
    else
    {
        AddError(FString::Printf(TEXT("Expected %d distances, got %d"), 
                                ExpectedDistances.Num(), CalculatedDistances.Num()));
    }

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【LOD计算测试】
 * 测试GPU LOD计算的准确性
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FGPULODCalculationTest,
    "Game.Performance.GPUComputeManager.LODCalculation",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FGPULODCalculationTest::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UGPUComputeManager* GPUManager = UGPUComputeManager::Get(TestWorld);
    
    if (!GPUManager)
    {
        AddError(TEXT("Failed to get GPUComputeManager instance"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // 创建测试数据（使用FMapChunk类型）
    TArray<FMapChunk> Chunks;
    FVector ViewerPosition(0.0f, 0.0f, 0.0f);

    // 生成不同距离的地图块
    for (int32 i = 0; i < 4; ++i)
    {
        FMapChunk Chunk;
        Chunk.ChunkCoordinate = FIntPoint(i * 5, 0); // 不同距离的块坐标
        Chunk.ChunkSize = 32;
        Chunk.CurrentLOD = ELODLevel::Medium;
        Chunk.bIsLoaded = true;
        Chunks.Add(Chunk);
    }

    // 执行LOD计算
    TArray<int32> CalculatedLODs;
    int32 TaskID = GPUManager->CalculateLODBatch_GPU(Chunks, ViewerPosition, CalculatedLODs);
    
    TestTrue(TEXT("LOD Task ID should be valid"), TaskID >= 0);

    // 验证LOD计算结果
    TArray<int32> ExpectedLODs = {0, 1, 2, 3};
    if (CalculatedLODs.Num() == ExpectedLODs.Num())
    {
        for (int32 i = 0; i < ExpectedLODs.Num(); ++i)
        {
            TestEqual(FString::Printf(TEXT("LOD %d should be correct"), i),
                     CalculatedLODs[i], ExpectedLODs[i]);
        }
    }
    else
    {
        AddError(FString::Printf(TEXT("Expected %d LOD values, got %d"), 
                                ExpectedLODs.Num(), CalculatedLODs.Num()));
    }

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【GPU/CPU混合计算测试】
 * 测试GPU和CPU混合计算的负载均衡策略
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FHybridComputeTest,
    "Game.Performance.GPUComputeManager.HybridCompute",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FHybridComputeTest::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UGPUComputeManager* GPUManager = UGPUComputeManager::Get(TestWorld);
    
    if (!GPUManager)
    {
        AddError(TEXT("Failed to get GPUComputeManager instance"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // 设置GPU计算阈值
    int32 GPUThreshold = 100;
    GPUManager->SetGPUComputeThreshold(GPUThreshold);

    // 测试小数据集（应该使用CPU）
    TArray<FVector> SmallDataset;
    for (int32 i = 0; i < 50; ++i) // 小于阈值
    {
        SmallDataset.Add(FVector(i * 10.0f, 0.0f, 0.0f));
    }

    TArray<float> SmallResults;
    // 转换为FIntPoint数组
    TArray<FIntPoint> SmallPointsA, SmallPointsB;
    for (int32 i = 0; i < SmallDataset.Num(); ++i)
    {
        SmallPointsA.Add(FIntPoint(0, 0));
        SmallPointsB.Add(FIntPoint(static_cast<int32>(SmallDataset[i].X), 0));
    }
    int32 SmallTaskID = GPUManager->CalculateDistancesBatch_GPU(SmallPointsA, SmallPointsB, SmallResults);
    TestTrue(TEXT("Small dataset task should be valid"), SmallTaskID >= 0);

    // 测试大数据集（应该使用GPU）
    TArray<FVector> LargeDataset;
    for (int32 i = 0; i < 200; ++i) // 大于阈值
    {
        LargeDataset.Add(FVector(i * 5.0f, 0.0f, 0.0f));
    }

    TArray<float> LargeResults;
    // 转换为FIntPoint数组
    TArray<FIntPoint> LargePointsA, LargePointsB;
    for (int32 i = 0; i < LargeDataset.Num(); ++i)
    {
        LargePointsA.Add(FIntPoint(0, 0));
        LargePointsB.Add(FIntPoint(static_cast<int32>(LargeDataset[i].X), 0));
    }
    int32 LargeTaskID = GPUManager->CalculateDistancesBatch_GPU(LargePointsA, LargePointsB, LargeResults);
    TestTrue(TEXT("Large dataset task should be valid"), LargeTaskID >= 0);

    // 验证结果数量正确
    TestEqual(TEXT("Small results count should match input"), SmallResults.Num(), SmallDataset.Num());
    TestEqual(TEXT("Large results count should match input"), LargeResults.Num(), LargeDataset.Num());

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【GPU计算统计测试】
 * 测试GPU计算统计信息的收集和报告
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FGPUComputeStatsTest,
    "Game.Performance.GPUComputeManager.ComputeStats",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FGPUComputeStatsTest::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UGPUComputeManager* GPUManager = UGPUComputeManager::Get(TestWorld);
    
    if (!GPUManager)
    {
        AddError(TEXT("Failed to get GPUComputeManager instance"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // 获取初始统计信息
    FGPUComputeStats InitialStats = GPUManager->GetGPUComputeStats();
    
    // 执行一些GPU计算任务
    TArray<FVector> TestPositions;
    for (int32 i = 0; i < 10; ++i)
    {
        TestPositions.Add(FVector(i * 10.0f, 0.0f, 0.0f));
    }

    TArray<float> Results;
    // 转换为FIntPoint数组
    TArray<FIntPoint> TestPointsA, TestPointsB;
    for (int32 i = 0; i < TestPositions.Num(); ++i)
    {
        TestPointsA.Add(FIntPoint(0, 0));
        TestPointsB.Add(FIntPoint(static_cast<int32>(TestPositions[i].X), 0));
    }
    GPUManager->CalculateDistancesBatch_GPU(TestPointsA, TestPointsB, Results);

    // 获取更新后的统计信息
    FGPUComputeStats UpdatedStats = GPUManager->GetGPUComputeStats();
    
    // 验证统计信息的合理性
    TestTrue(TEXT("Total tasks should be non-negative"), UpdatedStats.TotalTasks >= 0);
    TestTrue(TEXT("Completed tasks should not exceed total tasks"), 
             UpdatedStats.CompletedTasks <= UpdatedStats.TotalTasks);
    TestTrue(TEXT("Failed tasks should not exceed total tasks"), 
             UpdatedStats.FailedTasks <= UpdatedStats.TotalTasks);
    TestTrue(TEXT("Average execution time should be non-negative"), 
             UpdatedStats.AverageExecutionTime >= 0.0f);

    // 验证统计信息有所变化（表明任务被记录）
    TestTrue(TEXT("Statistics should have been updated"), 
             UpdatedStats.TotalTasks >= InitialStats.TotalTasks);

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【GPU计算错误处理测试】
 * 测试GPU计算的错误处理和恢复机制
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FGPUComputeErrorHandlingTest,
    "Game.Performance.GPUComputeManager.ErrorHandling",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FGPUComputeErrorHandlingTest::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UGPUComputeManager* GPUManager = UGPUComputeManager::Get(TestWorld);
    
    if (!GPUManager)
    {
        AddError(TEXT("Failed to get GPUComputeManager instance"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // 测试空数据集处理
    TArray<FVector> EmptyPositions;
    TArray<float> EmptyResults;
    // 空数据集测试
    TArray<FIntPoint> EmptyPointsA, EmptyPointsB;
    int32 EmptyTaskID = GPUManager->CalculateDistancesBatch_GPU(EmptyPointsA, EmptyPointsB, EmptyResults);
    
    // 空数据集应该被优雅处理
    TestTrue(TEXT("Empty dataset should be handled gracefully"), EmptyTaskID >= 0 || EmptyTaskID == -1);

    // 测试无效参数处理
    TArray<FVector> InvalidPositions;
    InvalidPositions.Add(FVector(FLT_MAX, FLT_MAX, FLT_MAX)); // 极大值
    InvalidPositions.Add(FVector(-FLT_MAX, -FLT_MAX, -FLT_MAX)); // 极小值
    
    TArray<float> InvalidResults;
    // 转换无效数据为FIntPoint
    TArray<FIntPoint> InvalidPointsA, InvalidPointsB;
    for (int32 i = 0; i < InvalidPositions.Num(); ++i)
    {
        InvalidPointsA.Add(FIntPoint(0, 0));
        InvalidPointsB.Add(FIntPoint(INT_MAX, INT_MAX)); // 使用极大值
    }
    int32 InvalidTaskID = GPUManager->CalculateDistancesBatch_GPU(InvalidPointsA, InvalidPointsB, InvalidResults);
    
    // 无效数据应该被处理而不崩溃
    TestTrue(TEXT("Invalid data should not cause crash"), true); // 如果到这里说明没有崩溃

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

#endif // WITH_DEV_AUTOMATION_TESTS
