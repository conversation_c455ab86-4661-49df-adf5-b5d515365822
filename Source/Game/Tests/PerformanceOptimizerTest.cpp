// Copyright Epic Games, Inc. All Rights Reserved.

#include "CoreMinimal.h"
#include "Misc/AutomationTest.h"
#include "Performance/PerformanceOptimizer.h"
#include "Performance/CacheManager.h"
#include "Performance/ObjectPoolManager.h"
#include "Performance/GPUComputeManager.h"
#include "LevelGen/MapUtil.h"
#include "Engine/World.h"
#include "Tests/AutomationCommon.h"

#if WITH_DEV_AUTOMATION_TESTS

/**
 * 【性能优化器单元测试】
 * 测试性能优化器的核心功能，包括数据分析、调试工具、对象池管理等
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FPerformanceOptimizerBasicTest,
    "Game.Performance.PerformanceOptimizer.BasicFunctionality",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FPerformanceOptimizerBasicTest::RunTest(const FString& Parameters)
{
    // 创建测试世界
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    if (!TestWorld)
    {
        AddError(TEXT("Failed to create test world"));
        return false;
    }

    // 创建性能优化器实例
    UPerformanceOptimizer* Optimizer = NewObject<UPerformanceOptimizer>(TestWorld);
    if (!Optimizer)
    {
        AddError(TEXT("Failed to create PerformanceOptimizer instance"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // 初始化优化器
    FPerformanceOptimizationParams TestParams;
    TestParams.bEnableObjectPooling = true;
    TestParams.bEnableCaching = true;
    TestParams.bEnableGPUCompute = true;
    TestParams.MaxCacheSize = 512;
    TestParams.ChunkSize = 16;

    Optimizer->Initialize(TestParams);

    // 测试基本功能
    TestTrue(TEXT("Optimizer should be initialized"), Optimizer->IsInitialized());
    
    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【数据熵计算测试】
 * 测试CalculateDataEntropy方法的准确性
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FDataEntropyCalculationTest,
    "Game.Performance.PerformanceOptimizer.DataEntropyCalculation",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FDataEntropyCalculationTest::RunTest(const FString& Parameters)
{
    // 创建测试世界和优化器
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UPerformanceOptimizer* Optimizer = NewObject<UPerformanceOptimizer>(TestWorld);
    
    FPerformanceOptimizationParams TestParams;
    Optimizer->Initialize(TestParams);

    // 创建测试数据 - 完全相同的数据（熵应该为0）
    TArray<FMapCell> UniformData;
    for (int32 i = 0; i < 100; ++i)
    {
        FMapCell Cell;
        Cell.SurfaceCoverType = ESurfaceCoverType::Grass;
        Cell.ZLevel = 1.0f;
        Cell.Temperature = 20.0f;
        UniformData.Add(Cell);
    }

    // 测试统一数据的熵值（应该接近0）
    float UniformEntropy = Optimizer->CalculateDataEntropy(UniformData);
    TestTrue(TEXT("Uniform data should have low entropy"), UniformEntropy < 0.1f);

    // 创建随机数据（熵应该较高）
    TArray<FMapCell> RandomData;
    for (int32 i = 0; i < 100; ++i)
    {
        FMapCell Cell;
        Cell.SurfaceCoverType = static_cast<ESurfaceCoverType>(FMath::RandRange(0, 5));
        Cell.ZLevel = FMath::FRandRange(0.0f, 100.0f);
        Cell.Temperature = FMath::FRandRange(-10.0f, 40.0f);
        RandomData.Add(Cell);
    }

    float RandomEntropy = Optimizer->CalculateDataEntropy(RandomData);
    TestTrue(TEXT("Random data should have higher entropy than uniform data"), 
             RandomEntropy > UniformEntropy);

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【重复模式检测测试】
 * 测试HasRepeatingPatterns方法的检测能力
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FRepeatingPatternsDetectionTest,
    "Game.Performance.PerformanceOptimizer.RepeatingPatternsDetection",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FRepeatingPatternsDetectionTest::RunTest(const FString& Parameters)
{
    // 创建测试世界和优化器
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UPerformanceOptimizer* Optimizer = NewObject<UPerformanceOptimizer>(TestWorld);
    
    FPerformanceOptimizationParams TestParams;
    Optimizer->Initialize(TestParams);

    // 创建有重复模式的数据
    TArray<FMapCell> PatternedData;
    for (int32 i = 0; i < 50; ++i)
    {
        // 创建重复的ABAB模式
        FMapCell CellA, CellB;
        CellA.SurfaceCoverType = ESurfaceCoverType::Grass;
        CellA.ZLevel = 1.0f;
        CellB.SurfaceCoverType = ESurfaceCoverType::Rock;
        CellB.ZLevel = 2.0f;
        
        PatternedData.Add(CellA);
        PatternedData.Add(CellB);
    }

    // 测试重复模式检测
    bool HasPatterns = Optimizer->HasRepeatingPatterns(PatternedData);
    TestTrue(TEXT("Should detect repeating patterns in patterned data"), HasPatterns);

    // 创建无重复模式的随机数据
    TArray<FMapCell> RandomData;
    for (int32 i = 0; i < 100; ++i)
    {
        FMapCell Cell;
        Cell.SurfaceCoverType = static_cast<ESurfaceCoverType>(FMath::RandRange(0, 5));
        Cell.ZLevel = FMath::FRandRange(0.0f, 100.0f);
        RandomData.Add(Cell);
    }

    bool HasRandomPatterns = Optimizer->HasRepeatingPatterns(RandomData);
    TestFalse(TEXT("Should not detect patterns in truly random data"), HasRandomPatterns);

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【对象池功能测试】
 * 测试对象池的获取和归还功能
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FObjectPoolFunctionalityTest,
    "Game.Performance.PerformanceOptimizer.ObjectPoolFunctionality",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FObjectPoolFunctionalityTest::RunTest(const FString& Parameters)
{
    // 创建测试世界和优化器
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UPerformanceOptimizer* Optimizer = NewObject<UPerformanceOptimizer>(TestWorld);
    
    FPerformanceOptimizationParams TestParams;
    TestParams.bEnableObjectPooling = true;
    Optimizer->Initialize(TestParams);

    // 测试MapCell对象池
    TSharedPtr<FMapCell> Cell1 = Optimizer->GetPooledMapCell();
    TestTrue(TEXT("Should be able to get pooled MapCell"), Cell1.IsValid());

    TSharedPtr<FMapCell> Cell2 = Optimizer->GetPooledMapCell();
    TestTrue(TEXT("Should be able to get second pooled MapCell"), Cell2.IsValid());
    TestTrue(TEXT("Two pooled objects should be different"), Cell1 != Cell2);

    // 归还对象到池中
    Optimizer->ReturnPooledMapCell(Cell1);
    Optimizer->ReturnPooledMapCell(Cell2);

    // 再次获取对象（应该重用之前的对象）
    TSharedPtr<FMapCell> Cell3 = Optimizer->GetPooledMapCell();
    TestTrue(TEXT("Should be able to reuse pooled MapCell"), Cell3.IsValid());

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【缓存系统测试】
 * 测试缓存数据的存储和检索功能
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FCacheSystemFunctionalityTest,
    "Game.Performance.PerformanceOptimizer.CacheSystemFunctionality",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FCacheSystemFunctionalityTest::RunTest(const FString& Parameters)
{
    // 创建测试世界和优化器
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UPerformanceOptimizer* Optimizer = NewObject<UPerformanceOptimizer>(TestWorld);
    
    FPerformanceOptimizationParams TestParams;
    TestParams.bEnableCaching = true;
    TestParams.MaxCacheSize = 1024;
    Optimizer->Initialize(TestParams);

    // 创建测试数据
    TArray<FMapCell> TestData;
    for (int32 i = 0; i < 10; ++i)
    {
        FMapCell Cell;
        Cell.SurfaceCoverType = ESurfaceCoverType::Grass;
        Cell.ZLevel = static_cast<float>(i);
        TestData.Add(Cell);
    }

    // 测试缓存存储
    FString CacheKey = TEXT("TestChunk_0_0");
    Optimizer->CacheMapData(CacheKey, TestData);

    // 测试缓存检索
    TArray<FMapCell> RetrievedData;
    bool bCacheHit = Optimizer->GetCachedMapData(CacheKey, RetrievedData);
    
    TestTrue(TEXT("Should find cached data"), bCacheHit);
    TestEqual(TEXT("Retrieved data should match original"), RetrievedData.Num(), TestData.Num());
    
    if (RetrievedData.Num() == TestData.Num())
    {
        for (int32 i = 0; i < TestData.Num(); ++i)
        {
            TestEqual(TEXT("Cell height should match"), RetrievedData[i].ZLevel, TestData[i].ZLevel);
        }
    }

    // 测试不存在的缓存键
    TArray<FMapCell> NonExistentData;
    bool bNonExistentHit = Optimizer->GetCachedMapData(TEXT("NonExistent"), NonExistentData);
    TestFalse(TEXT("Should not find non-existent cache"), bNonExistentHit);

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * 【性能统计测试】
 * 测试性能统计信息的收集和报告
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FPerformanceStatsTest,
    "Game.Performance.PerformanceOptimizer.PerformanceStats",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FPerformanceStatsTest::RunTest(const FString& Parameters)
{
    // 创建测试世界和优化器
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    UPerformanceOptimizer* Optimizer = NewObject<UPerformanceOptimizer>(TestWorld);
    
    FPerformanceOptimizationParams TestParams;
    Optimizer->Initialize(TestParams);

    // 执行一些操作来生成统计数据
    TArray<FMapCell> TestData;
    for (int32 i = 0; i < 100; ++i)
    {
        FMapCell Cell;
        Cell.SurfaceCoverType = ESurfaceCoverType::Grass;
        TestData.Add(Cell);
    }

    // 缓存一些数据
    Optimizer->CacheMapData(TEXT("StatsTest"), TestData);
    
    // 获取性能统计
    FPerformanceStats Stats = Optimizer->GetPerformanceStats();
    
    // 验证统计数据的合理性
    TestTrue(TEXT("Memory usage should be positive"), Stats.MemoryUsage >= 0);
    TestTrue(TEXT("Cache hit rate should be between 0 and 1"), 
             Stats.CacheHitRate >= 0.0f && Stats.CacheHitRate <= 1.0f);

    // 清理
    TestWorld->DestroyWorld(false);
    return true;
}

#endif // WITH_DEV_AUTOMATION_TESTS
