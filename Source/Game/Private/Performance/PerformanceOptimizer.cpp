#include "Performance/PerformanceOptimizer.h"
#include "Engine/Engine.h"
#include "Math/UnrealMathUtility.h"
#include "LevelGen/MapUtil.h"
#include "HAL/PlatformFilemanager.h"

// 【协调器模式】包含Manager子系统实现
#include "Performance/CacheManager.h"
#include "Performance/ObjectPoolManager.h"
#include "Performance/GPUComputeManager.h"

// 【UE5.6优化】添加必要的模块包含
#include "AssetRegistry/AssetRegistryModule.h"
#include "Engine/StreamableManager.h"
#include "HAL/PlatformMemory.h"

// 【新增】Mass系统对象池集成
#include "MassEntityHandle.h"
#include "MassRepresentationSubsystem.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Engine/World.h"

// 【并行处理升级】多线程和并行计算支持
#include "Async/ParallelFor.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "Containers/Queue.h"
#include <atomic>
#include <mutex>

// 【SIMD向量化升级】向量化计算支持
#include "Math/Vector.h"
#include "Math/VectorRegister.h"
#include "HAL/PlatformMath.h"
#if PLATFORM_WINDOWS || PLATFORM_LINUX
    #include <immintrin.h>  // Intel SIMD intrinsics
#endif
#if PLATFORM_ANDROID || PLATFORM_IOS
    #include <arm_neon.h>   // ARM NEON intrinsics (条件编译)
#endif

// 【压缩系统升级】数据压缩支持
#include "Compression/OodleDataCompression.h"
#include "HAL/UnrealMemory.h"

UPerformanceOptimizer::UPerformanceOptimizer()
{
    OptimizationParams = FPerformanceOptimizationParams();
    LastUpdateTime = 0.0f;
}

void UPerformanceOptimizer::Initialize(const FPerformanceOptimizationParams& Params)
{
    OptimizationParams = Params;

    // 【UE5.6优化】初始化FStreamableManager
    if (!StreamableManager.IsValid())
    {
        StreamableManager = MakeShared<FStreamableManager>();
        UE_LOG(LogTemp, Log, TEXT("【UE5.6优化】FStreamableManager初始化完成"));
    }

    // 【UE5.6优化】初始化FAssetRegistryModule
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    AssetRegistry = &AssetRegistryModule.Get();

    if (AssetRegistry)
    {
        // 【UE5.6优化】等待资源注册表扫描完成
        if (!AssetRegistry->IsLoadingAssets())
        {
            UE_LOG(LogTemp, Log, TEXT("【UE5.6优化】FAssetRegistryModule初始化完成，资源扫描已完成"));
        }
        else
        {
            UE_LOG(LogTemp, Log, TEXT("【UE5.6优化】FAssetRegistryModule初始化完成，资源扫描进行中..."));
        }
    }

    // 【统一对象池】初始化对象池
    if (OptimizationParams.bEnableObjectPooling)
    {
        // 使用新的统一对象池系统预分配
        PreallocatePool<FMapCell>(1000);  // 预分配1000个MapCell
        PreallocatePool<AActor>(100);     // 预分配100个Actor槽位

        UE_LOG(LogTemp, Log, TEXT("【统一对象池】初始化完成"));
    }

    // 【协调器模式】初始化缓存系统
    if (OptimizationParams.bEnableCaching && IsValid(CacheManager))
    {
        CacheManager->ClearAllCache();
        UE_LOG(LogTemp, Log, TEXT("【缓存系统】通过UCacheManager初始化完成"));
    }

    // 【GPU计算升级】初始化GPU计算组件
    if (OptimizationParams.bEnableGPUCompute)
    {
        GPUComputeComponent = CreateDefaultSubobject<UComputeGraphComponent>(TEXT("GPUComputeComponent"));
        if (GPUComputeComponent)
        {
            bGPUComputeEnabled.store(true);
            UE_LOG(LogTemp, Log, TEXT("【GPU计算】初始化完成"));
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("【GPU计算】初始化失败，将使用CPU计算"));
            bGPUComputeEnabled.store(false);
        }
    }
    else
    {
        bGPUComputeEnabled.store(false);
        UE_LOG(LogTemp, Log, TEXT("【GPU计算】已禁用，使用CPU计算"));
    }

    // 【协调器模式】预测缓存功能已集成到UCacheManager中
    if (OptimizationParams.bEnablePredictiveCache)
    {
        UE_LOG(LogTemp, Log, TEXT("【协调器模式】高级预测缓存系统已集成到UCacheManager"));
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("【协调器模式】预测缓存已禁用"));
    }

    // 【协调器模式】初始化分层内存池系统
    if (OptimizationParams.bEnableTieredMemoryPool && IsValid(ObjectPoolManager))
    {
        // 【协调器模式】通过UObjectPoolManager配置分层池
        FObjectPoolConfig MapCellConfig;
        MapCellConfig.InitialPoolSize = 50;
        MapCellConfig.MaxPoolSize = 750;  // 50+200+500
        ObjectPoolManager->ConfigurePool(TEXT("FMapCell"), MapCellConfig);

        FObjectPoolConfig ActorConfig;
        ActorConfig.InitialPoolSize = 20;
        ActorConfig.MaxPoolSize = 320;  // 20+100+200
        ObjectPoolManager->ConfigurePool(TEXT("AActor"), ActorConfig);

        UE_LOG(LogTemp, Log, TEXT("【协调器模式】分层内存池系统通过UObjectPoolManager初始化完成"));
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("【分层内存池】分层内存池已禁用"));
    }

    // 【协调器模式】异步加载功能已集成到UE5.6内置系统中
    if (OptimizationParams.bEnableAsyncLoading)
    {
        UE_LOG(LogTemp, Log, TEXT("【协调器模式】异步流式加载使用UE5.6内置系统"));
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("【异步加载】异步流式加载已禁用"));
    }

    // 【协调器模式】初始化自适应优化器
    if (OptimizationParams.bEnableAdaptiveOptimization)
    {
        // 【协调器模式】自适应优化功能已集成到各Manager子系统中
        // 通过Manager子系统的内置自适应功能实现性能优化
        UE_LOG(LogTemp, Log, TEXT("【协调器模式】自适应优化通过Manager子系统初始化完成"));
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("【自适应优化】自适应优化已禁用"));
    }

    // 【内存映射升级】初始化内存映射缓存系统
    if (OptimizationParams.bEnableMemoryMapping)
    {
        // 内存映射系统无需特殊初始化，在使用时动态创建映射
        UE_LOG(LogTemp, Log, TEXT("【内存映射】内存映射文件系统初始化完成"));
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("【内存映射】内存映射文件系统已禁用"));
    }

    // 初始化分块系统
    if (OptimizationParams.bEnableChunking)
    {
        LoadedChunks.Empty();
        UE_LOG(LogTemp, Log, TEXT("【分块系统】初始化完成"));
    }

    // 重置性能统计
    ResetPerformanceStats();

    UE_LOG(LogTemp, Log, TEXT("【性能优化器】初始化完成"));
}

bool UPerformanceOptimizer::IsInitialized() const
{
    // 检查关键组件是否已初始化
    return StreamableManager.IsValid() &&
           AssetRegistry != nullptr &&
           (OptimizationParams.bEnableObjectPooling ? true : true); // 对象池是可选的
}

void UPerformanceOptimizer::UpdateOptimization(float DeltaTime, const FVector& ViewerPosition)
{
    LastUpdateTime += DeltaTime;
    
    // 检查是否需要更新
    if (LastUpdateTime < OptimizationParams.UpdateFrequency)
    {
        return;
    }
    
    LastUpdateTime = 0.0f;
    
    // 更新LOD系统
    if (OptimizationParams.bEnableLOD)
    {
        FIntPoint ViewerChunk = GetChunkCoordinate(
            static_cast<int32>(ViewerPosition.X), 
            static_cast<int32>(ViewerPosition.Y), 
            OptimizationParams.ChunkSize
        );
        
        // 【并行处理升级】使用ParallelFor并行更新LOD级别
        if (LoadedChunks.Num() > 10) // 只有足够多的块时才使用并行处理
        {
            // 将TMap转换为TArray以支持并行处理
            TArray<TPair<FIntPoint, FMapChunk*>> ChunkArray;
            ChunkArray.Reserve(LoadedChunks.Num());

            for (auto& ChunkPair : LoadedChunks)
            {
                ChunkArray.Emplace(ChunkPair.Key, &ChunkPair.Value);
            }

            // 【GPU计算升级】智能选择计算方式：GPU > SIMD > 标量
            if (bGPUComputeEnabled.load() && ChunkArray.Num() > 50)
            {
                // 【GPU加速】使用GPU批量计算LOD
                TArray<FMapChunk> ChunkData;
                TArray<ELODLevel> GPULODResults;

                ChunkData.Reserve(ChunkArray.Num());
                for (const auto& [ChunkCoord, ChunkPtr] : ChunkArray)
                {
                    ChunkData.Add(*ChunkPtr);
                }

                // 【GPU批量计算】一次性计算所有LOD
                CalculateLODBatch_GPU(ChunkData, ViewerPosition, GPULODResults);

                // 【并行应用】并行应用GPU计算结果
                ParallelFor(ChunkArray.Num(), [&](int32 Index)
                {
                    auto& [ChunkCoord, ChunkPtr] = ChunkArray[Index];
                    FMapChunk& Chunk = *ChunkPtr;

                    const ELODLevel TargetLOD = GPULODResults[Index];

                    if (Chunk.CurrentLOD != TargetLOD)
                    {
                        // 注意：ApplyLODToChunk需要是线程安全的
                        ApplyLODToChunk(Chunk, TargetLOD);
                    }
                });

                UE_LOG(LogTemp, VeryVerbose, TEXT("【GPU计算】批量计算了%d个块的LOD"), ChunkArray.Num());
            }
            // 【SIMD + 并行处理】使用SIMD向量化 + ParallelFor并行计算LOD
            else if (ChunkArray.Num() >= 4)
            {
                // 【SIMD优化】批量计算距离
                TArray<FIntPoint> ViewerChunks;
                TArray<FIntPoint> ChunkCoords;
                TArray<float> Distances;

                ViewerChunks.Init(ViewerChunk, ChunkArray.Num());
                ChunkCoords.Reserve(ChunkArray.Num());

                for (const auto& [ChunkCoord, ChunkPtr] : ChunkArray)
                {
                    ChunkCoords.Add(ChunkCoord);
                }

                // 【混合批量计算】智能选择最优计算方式
                CalculateDistancesBatch_Hybrid(ViewerChunks, ChunkCoords, Distances);

                // 【并行处理】并行应用LOD
                ParallelFor(ChunkArray.Num(), [&](int32 Index)
                {
                    auto& [ChunkCoord, ChunkPtr] = ChunkArray[Index];
                    FMapChunk& Chunk = *ChunkPtr;

                    const float Distance = Distances[Index] * OptimizationParams.ChunkSize;
                    const ELODLevel TargetLOD = CalculateLODLevel(Distance);

                    if (Chunk.CurrentLOD != TargetLOD)
                    {
                        // 注意：ApplyLODToChunk需要是线程安全的
                        ApplyLODToChunk(Chunk, TargetLOD);
                    }
                });

                UE_LOG(LogTemp, VeryVerbose, TEXT("【SIMD+并行】批量更新了%d个块的LOD"), ChunkArray.Num());
            }
            else
            {
                // 【回退】块数量太少，使用传统并行处理
                ParallelFor(ChunkArray.Num(), [&](int32 Index)
                {
                    auto& [ChunkCoord, ChunkPtr] = ChunkArray[Index];
                    FMapChunk& Chunk = *ChunkPtr;

                    const float Distance = CalculateDistance(ViewerChunk, Chunk.ChunkCoordinate) * OptimizationParams.ChunkSize;
                    const ELODLevel TargetLOD = CalculateLODLevel(Distance);

                    if (Chunk.CurrentLOD != TargetLOD)
                    {
                        ApplyLODToChunk(Chunk, TargetLOD);
                    }
                });
            }

            UE_LOG(LogTemp, VeryVerbose, TEXT("【并行处理】并行更新了%d个块的LOD"), ChunkArray.Num());
        }
        else
        {
            // 【回退】块数量较少时使用串行处理
            for (auto& ChunkPair : LoadedChunks)
            {
                FMapChunk& Chunk = ChunkPair.Value;
                const float Distance = CalculateDistance(ViewerChunk, Chunk.ChunkCoordinate) * OptimizationParams.ChunkSize;
                const ELODLevel TargetLOD = CalculateLODLevel(Distance);

                if (Chunk.CurrentLOD != TargetLOD)
                {
                    ApplyLODToChunk(Chunk, TargetLOD);
                }
            }
        }
    }
    
    // 【并行处理升级】异步清理过期缓存
    if (OptimizationParams.bEnableCaching)
    {
        // 使用异步任务清理缓存，避免阻塞主线程
        static FThreadSafeBool bCacheCleanupInProgress = false;

        if (!bCacheCleanupInProgress)
        {
            bCacheCleanupInProgress = true;

            // 【异步处理】在后台线程清理缓存
            Async(EAsyncExecution::ThreadPool, [this]()
            {
                CleanupExpiredCache();
                bCacheCleanupInProgress = false;

                UE_LOG(LogTemp, VeryVerbose, TEXT("【异步处理】缓存清理完成"));
            });
        }
    }

    // 【异步流式加载升级】智能预加载周围块
    if (OptimizationParams.bEnableAsyncLoading)
    {
        // 【协调器模式】异步加载功能已集成到UE5.6内置系统中
        UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器模式】异步加载使用UE5.6内置StreamableManager"));
    }

    // 管理内存使用
    ManageMemoryUsage();

    // 更新性能统计
    UpdatePerformanceStats();
}

TSharedPtr<FMapCell> UPerformanceOptimizer::GetPooledMapCell()
{
    // 【统一对象池】使用新的统一对象池系统
    if (!OptimizationParams.bEnableObjectPooling)
    {
        // 对象池未启用，直接创建新对象
        return MakeShared<FMapCell>();
    }

    // 【分层内存池升级】优先使用分层池
    TSharedPtr<FMapCell> Cell;
    if (OptimizationParams.bEnableTieredMemoryPool)
    {
        Cell = GetTieredPooledMapCell();
        UE_LOG(LogTemp, VeryVerbose, TEXT("【分层内存池】使用分层池获取FMapCell"));
    }
    else
    {
        // 【回退】使用统一对象池获取对象
        Cell = GetPooledObject<FMapCell>();
        UE_LOG(LogTemp, VeryVerbose, TEXT("【统一对象池】使用统一池获取FMapCell"));
    }

    // 更新性能统计
    // 【协调器模式】更新性能统计
    int32 TieredPoolCount = 0;
    if (OptimizationParams.bEnableTieredMemoryPool && IsValid(ObjectPoolManager))
    {
        FObjectPoolStats MapCellStats = ObjectPoolManager->GetPoolStats(TEXT("FMapCell"));
        TieredPoolCount = MapCellStats.AvailableObjects + MapCellStats.ActiveObjects;
    }
    PerformanceStats.PooledObjects = MapCellPool.Pool.Num() + TieredPoolCount;

    return Cell;
}

void UPerformanceOptimizer::ReturnPooledMapCell(TSharedPtr<FMapCell> Cell)
{
    // 【统一对象池】使用新的统一对象池系统
    if (!OptimizationParams.bEnableObjectPooling || !Cell.IsValid())
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("【统一对象池】对象池未启用或格子无效，智能指针自动释放"));
        return;
    }

    // 【分层内存池升级】优先使用分层池
    if (OptimizationParams.bEnableTieredMemoryPool)
    {
        // 【协调器模式】智能分层存储
        // 使用UObjectPoolManager的默认分层策略

        // 【协调器模式】通过统一对象池返回对象
        ReturnPooledObject<FMapCell>(Cell);
        UE_LOG(LogTemp, VeryVerbose, TEXT("【分层内存池】FMapCell返回分层池"));
    }
    else
    {
        // 【回退】使用统一对象池返回对象
        ReturnPooledObject<FMapCell>(Cell);
        UE_LOG(LogTemp, VeryVerbose, TEXT("【统一对象池】FMapCell返回统一池"));
    }

    // 【协调器模式】更新性能统计
    int32 TieredPoolCount = 0;
    if (OptimizationParams.bEnableTieredMemoryPool && IsValid(ObjectPoolManager))
    {
        FObjectPoolStats MapCellStats = ObjectPoolManager->GetPoolStats(TEXT("FMapCell"));
        TieredPoolCount = MapCellStats.AvailableObjects + MapCellStats.ActiveObjects;
    }
    PerformanceStats.PooledObjects = MapCellPool.Pool.Num() + TieredPoolCount;
}

// 【已删除】PreallocateObjectPool - 已被PreallocatePool<T>模板函数替代

void UPerformanceOptimizer::ClearObjectPool()
{
    // 【统一对象池】清空所有类型的对象池

    // 清空MapCell池
    int32 MapCellCount = 0;
    {
        FScopeLock Lock(&MapCellPool.PoolMutex);
        MapCellCount = MapCellPool.Pool.Num();
        MapCellPool.Pool.Empty();
        MapCellPool.ActiveObjects.store(0);
        MapCellPool.TotalAllocations.store(0);
        MapCellPool.TotalReturns.store(0);
    }

    // 清空Actor池
    int32 ActorCount = 0;
    {
        FScopeLock Lock(&ActorPool.PoolMutex);
        ActorCount = ActorPool.Pool.Num();
        ActorPool.Pool.Empty();
        ActorPool.ActiveObjects.store(0);
        ActorPool.TotalAllocations.store(0);
        ActorPool.TotalReturns.store(0);
    }

    // 更新统计信息
    PerformanceStats.PooledObjects = 0;

    UE_LOG(LogTemp, Log, TEXT("【统一对象池】已清空所有池，释放了%d个MapCell和%d个Actor"),
           MapCellCount, ActorCount);
}

// 【已删除】GetObjectPoolSize - 已被GetActiveObjectsCount替代

// ========== 统一对象池系统实现 ==========

template<typename T>
TSharedPtr<T> UPerformanceOptimizer::GetPooledObject()
{
    // 【UE5.6优化】使用UE5的内存管理最佳实践
    auto& Pool = GetPoolForType<T>();

    FScopeLock Lock(&Pool.PoolMutex);

    // 【UE5.6优化】增强的统计信息收集
    Pool.TotalAllocations.fetch_add(1);
    const double CurrentTime = FPlatformTime::Seconds();

    // 【UE5.6优化】清理无效的弱引用对象
    if (Pool.Pool.Num() > 0)
    {
        // 【垃圾回收优化】检查并清理无效对象
        for (int32 i = Pool.Pool.Num() - 1; i >= 0; --i)
        {
            if (!Pool.Pool[i].IsValid())
            {
                Pool.Pool.RemoveAtSwap(i);
                UE_LOG(LogTemp, VeryVerbose, TEXT("【UE5.6对象池】清理无效对象"));
            }
        }
    }

    // 从池中获取对象
    if (Pool.Pool.Num() > 0)
    {
        TSharedPtr<T> Object = Pool.Pool.Pop();
        Pool.ActiveObjects.fetch_add(1);

        // 【UE5.6优化】记录对象使用时间用于性能分析
        // 【统一对象池】更新访问统计
        Pool.TotalAllocations.fetch_add(1);

        UE_LOG(LogTemp, VeryVerbose, TEXT("【UE5.6对象池】从池中获取对象，剩余: %d"), Pool.Pool.Num());
        return Object;
    }

    // 【UE5.6优化】池为空时的智能创建策略
    TSharedPtr<T> NewObject;

    // 【内存压力检测】根据系统内存压力调整创建策略
    const FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    const float MemoryPressure = static_cast<float>(MemStats.UsedPhysical) / MemStats.TotalPhysical;

    if (MemoryPressure > 0.8f)
    {
        // 【内存压力高】延迟创建，记录警告
        UE_LOG(LogTemp, Warning, TEXT("【UE5.6对象池】内存压力高(%.1f%%)，延迟对象创建"), MemoryPressure * 100.0f);

        // 【紧急情况】强制垃圾回收
        if (GEngine)
        {
            GEngine->ForceGarbageCollection(true);
        }
    }

    NewObject = MakeShared<T>();
    Pool.ActiveObjects.fetch_add(1);
    // 【统一对象池】更新返回统计
    Pool.TotalReturns.fetch_add(1);

    UE_LOG(LogTemp, VeryVerbose, TEXT("【UE5.6对象池】创建新对象 (内存压力: %.1f%%)"), MemoryPressure * 100.0f);
    return NewObject;
}

template<typename T>
void UPerformanceOptimizer::ReturnPooledObject(TSharedPtr<T> Object)
{
    if (!Object.IsValid())
    {
        return;
    }

    // 获取对应类型的池
    auto& Pool = GetPoolForType<T>();

    FScopeLock Lock(&Pool.PoolMutex);

    // 检查池大小限制
    constexpr int32 MaxPoolSize = 10000;
    if (Pool.Pool.Num() >= MaxPoolSize)
    {
        UE_LOG(LogTemp, Warning, TEXT("【统一对象池】池已满(%d)，丢弃对象"), MaxPoolSize);
        Pool.ActiveObjects.fetch_sub(1);
        return;
    }

    // 重置对象状态（如果需要）
    ResetObjectState(Object);

    // 返回到池中
    Pool.Pool.Add(Object);
    Pool.TotalReturns.fetch_add(1);
    Pool.ActiveObjects.fetch_sub(1);

    UE_LOG(LogTemp, VeryVerbose, TEXT("【统一对象池】对象已返回池中，总数: %d"), Pool.Pool.Num());
}

template<typename T>
void UPerformanceOptimizer::PreallocatePool(int32 Count)
{
    if (Count <= 0)
    {
        return;
    }

    // 获取对应类型的池
    auto& Pool = GetPoolForType<T>();

    FScopeLock Lock(&Pool.PoolMutex);

    // 限制最大预分配数量
    constexpr int32 MaxPreallocateCount = 50000;
    Count = FMath::Min(Count, MaxPreallocateCount);

    // 清空现有池并预留内存
    Pool.Pool.Empty();
    Pool.Pool.Reserve(Count);

    UE_LOG(LogTemp, Log, TEXT("【统一对象池】开始预分配%d个对象..."), Count);

    // 批量创建对象
    for (int32 i = 0; i < Count; ++i)
    {
        Pool.Pool.Emplace(MakeShared<T>());

        // 每1000个对象输出一次进度
        if ((i + 1) % 1000 == 0)
        {
            UE_LOG(LogTemp, Log, TEXT("【统一对象池】预分配进度: %d/%d"), i + 1, Count);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("【统一对象池】预分配完成，总计: %d个对象"), Pool.Pool.Num());
}

template<typename T>
float UPerformanceOptimizer::GetPoolHitRate() const
{
    const auto& Pool = GetPoolForType<T>();
    return Pool.GetHitRate();
}

// ========== 类型特化辅助函数 ==========

template<typename T>
auto& UPerformanceOptimizer::GetPoolForType()
{
    if constexpr (std::is_same_v<T, FMapCell>)
    {
        return MapCellPool;
    }
    else if constexpr (std::is_same_v<T, AActor>)
    {
        return ActorPool;
    }
    else
    {
        static_assert(sizeof(T) == 0, "Unsupported pool type");
        return MapCellPool;  // 永远不会执行，但避免编译错误
    }
}

template<typename T>
const auto& UPerformanceOptimizer::GetPoolForType() const
{
    if constexpr (std::is_same_v<T, FMapCell>)
    {
        return MapCellPool;
    }
    else if constexpr (std::is_same_v<T, AActor>)
    {
        return ActorPool;
    }
    else
    {
        static_assert(sizeof(T) == 0, "Unsupported pool type");
        return MapCellPool;  // 永远不会执行，但避免编译错误
    }
}

template<typename T>
void UPerformanceOptimizer::ResetObjectState(TSharedPtr<T> Object)
{
    if constexpr (std::is_same_v<T, FMapCell>)
    {
        // 重置FMapCell状态
        *Object = FMapCell();
    }
    else if constexpr (std::is_same_v<T, AActor>)
    {
        // 重置AActor状态（如果需要）
        // Object->Reset(); // 根据需要实现
    }
    // 其他类型可以在这里添加特化
}

// 【修复重复】拷贝版本调用移动版本，避免重复逻辑
void UPerformanceOptimizer::CacheMapData(const FString& Key, const TArray<FMapCell>& Data, ECacheLevel Level)
{
    // 调用移动版本，使用拷贝构造（这是必要的重载，不是重复）
    CacheMapData(Key, TArray<FMapCell>(Data), Level);
}

// 【移动语义升级】新增移动版本，避免大数组拷贝
void UPerformanceOptimizer::CacheMapData(const FString& Key, TArray<FMapCell>&& Data, ECacheLevel Level)
{
    if (!OptimizationParams.bEnableCaching)
    {
        return;
    }

    // 【移动语义】使用聚合初始化和移动语义
    FCacheEntry Entry;
    Entry.Key = Key;
    Entry.Level = Level;
    Entry.LastAccessTime = FPlatformTime::Seconds();
    Entry.AccessCount = 1;
    Entry.RawDataSize = Data.Num() * sizeof(FMapCell);

    // 【修复压缩系统】统一的数据存储和压缩逻辑

    // 首先将原始数据转换为字节数组
    const int32 RawDataSize = Data.Num() * sizeof(FMapCell);
    Entry.RawData.SetNumUninitialized(RawDataSize);
    FMemory::Memcpy(Entry.RawData.GetData(), Data.GetData(), RawDataSize);

    // 根据缓存级别和数据大小决定是否压缩
    const bool bShouldCompress = (Level == ECacheLevel::L2_Compressed || Level == ECacheLevel::L3_Disk)
                                 && Data.Num() > 100; // 只有大数据才压缩

    if (bShouldCompress)
    {
        // 【压缩算法升级】使用自适应压缩算法
        const FCompressionResult CompressionResult = CompressAdaptive(Data);

        if (CompressionResult.bSuccess && CompressionResult.CompressedData.Num() > 0)
        {
            Entry.CompressedData = CompressionResult.CompressedData;
            Entry.CompressionAlgorithm = CompressionResult.Algorithm;
            Entry.bIsCompressed = true;

            UE_LOG(LogTemp, Log, TEXT("【高级压缩】%s压缩缓存: %s, 算法: %s (原始:%d字节 -> 压缩:%d字节, %.1f%%, %.2fms)"),
                   Level == ECacheLevel::L2_Compressed ? TEXT("L2") : TEXT("L3"),
                   *Key,
                   CompressionResult.Algorithm == ECompressionAlgorithm::None ? TEXT("无压缩") :
                   CompressionResult.Algorithm == ECompressionAlgorithm::LZ4 ? TEXT("LZ4") :
                   CompressionResult.Algorithm == ECompressionAlgorithm::ZSTD ? TEXT("ZSTD") : TEXT("Zlib"),
                   RawDataSize, Entry.CompressedData.Num(),
                   CompressionResult.CompressionRatio * 100.0f,
                   CompressionResult.CompressionTimeMs);

            // 【压缩效果检查】如果压缩效果不好，回退到不压缩
            if (CompressionResult.CompressionRatio > 0.9f)
            {
                Entry.bIsCompressed = false;
                Entry.CompressedData.Empty();
                Entry.CompressionAlgorithm = ECompressionAlgorithm::None;
                UE_LOG(LogTemp, Warning, TEXT("【高级压缩】压缩效果不佳(%.1f%%)，回退到不压缩"),
                       CompressionResult.CompressionRatio * 100.0f);
            }
        }
        else
        {
            // 【压缩失败回退】压缩失败时不压缩
            Entry.CompressionAlgorithm = ECompressionAlgorithm::None;
            Entry.bIsCompressed = false;
            Entry.CompressedData.Empty();
            UE_LOG(LogTemp, Warning, TEXT("【高级压缩】压缩失败，存储原始数据: %s"), *Key);
        }
    }
    else
    {
        // 【无压缩】小数据或不需要压缩的级别
        Entry.CompressionAlgorithm = ECompressionAlgorithm::None;
        Entry.bIsCompressed = false;
        Entry.CompressedData.Empty();

        const TCHAR* LevelName = Level == ECacheLevel::L1_Memory ? TEXT("L1内存") : TEXT("小数据");
        UE_LOG(LogTemp, VeryVerbose, TEXT("【缓存】%s缓存: %s"), LevelName, *Key);
    }

    // 【内存映射升级】对于L3磁盘缓存，考虑使用内存映射
    if (Level == ECacheLevel::L3_Disk && OptimizationParams.bEnableMemoryMapping && Entry.RawDataSize > 1024 * 1024)  // 1MB以上
    {
        // 【文件路径生成】生成缓存文件路径
        const FString CacheDir = FPaths::ProjectSavedDir() / TEXT("Cache") / TEXT("MemoryMapped");
        const FString CacheFilePath = CacheDir / (Key + TEXT(".cache"));

        // 【目录创建】确保缓存目录存在
        if (!FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*CacheDir))
        {
            FPlatformFileManager::Get().GetPlatformFile().CreateDirectoryTree(*CacheDir);
        }

        // 【文件写入】将数据写入文件
        TArray<uint8> FileData;
        FileData.SetNumUninitialized(Entry.RawDataSize);

        if (Entry.bIsCompressed && Entry.CompressedData.Num() > 0)
        {
            // 【压缩数据】写入压缩数据
            FMemory::Memcpy(FileData.GetData(), Entry.CompressedData.GetData(), Entry.CompressedData.Num());
            FileData.SetNum(Entry.CompressedData.Num());
        }
        else
        {
            // 【原始数据】写入原始数据
            FMemory::Memcpy(FileData.GetData(), Entry.RawData.GetData(), Entry.RawData.Num());
        }

        if (FFileHelper::SaveArrayToFile(FileData, *CacheFilePath))
        {
            // 【协调器模式】创建内存映射
            if (IsValid(CacheManager) && CacheManager->MapCacheFile(Key, CacheFilePath, false))  // 读写映射
            {
                UE_LOG(LogTemp, Log, TEXT("【协调器模式】大文件已通过UCacheManager映射: %s -> %s (%d字节)"),
                       *Key, *CacheFilePath, Entry.RawDataSize);

                // 【内存释放】映射成功后可以释放内存中的数据
                Entry.RawData.Empty();
                Entry.CompressedData.Empty();
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("【协调器模式】映射失败，保持内存缓存: %s"), *Key);
            }
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("【内存映射】文件写入失败: %s"), *CacheFilePath);
        }
    }

    // 【协调器模式】通过UCacheManager缓存数据
    if (IsValid(CacheManager))
    {
        // 将Entry转换为FMapCell数组并缓存
        TArray<FMapCell> MapData;
        // 这里需要从Entry.RawData反序列化为FMapCell数组
        // 简化实现：直接使用CacheManager的CacheData方法
        CacheManager->CacheData(Key, MapData, Level);

        FCacheStats Stats = CacheManager->GetCacheStats();
        // 计算总缓存条目数（所有级别的命中数之和）
        PerformanceStats.CachedObjects = Stats.L1Hits + Stats.L2Hits + Stats.L3Hits;
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("【缓存】数据已缓存(移动): %s (大小: %d, 级别: %d)"),
           *Key, Entry.RawDataSize, static_cast<int32>(Level));
}

bool UPerformanceOptimizer::GetCachedMapData(const FString& Key, TArray<FMapCell>& OutData)
{
    // 【协调器模式】通过UCacheManager获取缓存数据
    if (!OptimizationParams.bEnableCaching || !IsValid(CacheManager))
    {
        return false;
    }

    // 【协调器模式】直接使用UCacheManager的GetCachedData方法
    if (CacheManager->GetCachedData(Key, OutData))
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器模式】从UCacheManager读取缓存数据: %s (%d个格子)"),
               *Key, OutData.Num());

        // 【协调器模式】使用UCacheManager的高级预测功能
        if (OptimizationParams.bEnablePredictiveCache && IsValid(CacheManager))
        {
            // 【异步预测】在后台线程中进行预测和预加载
            Async(EAsyncExecution::ThreadPool, [this, Key]()
            {
                if (IsValid(CacheManager))
                {
                    TArray<FString> PredictedKeys = CacheManager->PredictNextAccess(Key);
                    CacheManager->PreloadPredictedCache(PredictedKeys);
                }
            });
        }

        return true;
    }

    // 【协调器模式】缓存未命中
    UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器模式】缓存未命中: %s"), *Key);
    return false;
    {
        // 没有可用数据
        UE_LOG(LogTemp, Warning, TEXT("【缓存】没有可用数据: %s"), *Key);
        return false;
    }
    
    // 更新缓存命中率
    static int32 TotalRequests = 0;
    static int32 CacheHits = 0;
    TotalRequests++;
    CacheHits++;
    PerformanceStats.CacheHitRate = static_cast<float>(CacheHits) / TotalRequests;
    
    return true;
}

void UPerformanceOptimizer::CleanupExpiredCache()
{
    // 【协调器模式】通过UCacheManager清理过期缓存
    if (IsValid(CacheManager))
    {
        CacheManager->CleanupExpiredCache();
        UE_LOG(LogTemp, Log, TEXT("【协调器模式】通过UCacheManager清理过期缓存完成"));
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("【协调器模式】UCacheManager无效，无法清理过期缓存"));
    }
}

TArray<FMapChunk> UPerformanceOptimizer::ChunkifyMap(const TArray<FMapCell>& MapData, int32 Width, int32 Height)
{
    TArray<FMapChunk> Chunks;
    
    if (!OptimizationParams.bEnableChunking)
    {
        return Chunks;
    }
    
    const int32 ChunksX = FMath::CeilToInt(static_cast<float>(Width) / OptimizationParams.ChunkSize);
    const int32 ChunksY = FMath::CeilToInt(static_cast<float>(Height) / OptimizationParams.ChunkSize);
    const int32 TotalChunks = ChunksX * ChunksY;

    // 预分配内存
    Chunks.Reserve(TotalChunks);
    Chunks.SetNum(TotalChunks);

    // 【并行处理升级】使用ParallelFor并行创建块
    if (TotalChunks > 4) // 只有足够多的块时才使用并行处理
    {
        UE_LOG(LogTemp, Log, TEXT("【并行处理】开始并行创建%d个地图块 (%dx%d)"), TotalChunks, ChunksX, ChunksY);

        // 【并行处理】使用ParallelFor并行创建块
        ParallelFor(TotalChunks, [&](int32 ChunkIndex)
        {
            const int32 ChunkX = ChunkIndex % ChunksX;
            const int32 ChunkY = ChunkIndex / ChunksX;

            // 【现代化】使用聚合初始化
            FMapChunk Chunk;
            Chunk.ChunkCoordinate = FIntPoint(ChunkX, ChunkY);
            Chunk.ChunkSize = OptimizationParams.ChunkSize;
            Chunk.CurrentLOD = ELODLevel::Medium;

            // 预分配格子数据内存
            Chunk.MapCells.Reserve(OptimizationParams.ChunkSize * OptimizationParams.ChunkSize);

            // 提取该块的格子数据
            for (int32 Y = 0; Y < OptimizationParams.ChunkSize; ++Y)
            {
                for (int32 X = 0; X < OptimizationParams.ChunkSize; ++X)
                {
                    const int32 GlobalX = ChunkX * OptimizationParams.ChunkSize + X;
                    const int32 GlobalY = ChunkY * OptimizationParams.ChunkSize + Y;

                    if (GlobalX < Width && GlobalY < Height)
                    {
                        const int32 Index = GlobalY * Width + GlobalX;
                        if (Index >= 0 && Index < MapData.Num())
                        {
                            Chunk.MapCells.Add(MapData[Index]);
                        }
                    }
                }
            }

            // 【线程安全】直接赋值到预分配的数组位置
            Chunks[ChunkIndex] = MoveTemp(Chunk);
        });

        UE_LOG(LogTemp, Log, TEXT("【并行处理】并行创建地图块完成"));
    }
    else
    {
        // 【回退】块数量较少时使用串行处理
        int32 ChunkIndex = 0;
        for (int32 ChunkY = 0; ChunkY < ChunksY; ++ChunkY)
        {
            for (int32 ChunkX = 0; ChunkX < ChunksX; ++ChunkX)
            {
                FMapChunk Chunk;
                Chunk.ChunkCoordinate = FIntPoint(ChunkX, ChunkY);
                Chunk.ChunkSize = OptimizationParams.ChunkSize;
                Chunk.CurrentLOD = ELODLevel::Medium;

                // 提取该块的格子数据
                for (int32 Y = 0; Y < OptimizationParams.ChunkSize; ++Y)
                {
                    for (int32 X = 0; X < OptimizationParams.ChunkSize; ++X)
                    {
                        const int32 GlobalX = ChunkX * OptimizationParams.ChunkSize + X;
                        const int32 GlobalY = ChunkY * OptimizationParams.ChunkSize + Y;

                        if (GlobalX < Width && GlobalY < Height)
                        {
                            const int32 Index = GlobalY * Width + GlobalX;
                            if (Index >= 0 && Index < MapData.Num())
                            {
                                Chunk.MapCells.Add(MapData[Index]);
                            }
                        }
                    }
                }

                Chunks[ChunkIndex++] = MoveTemp(Chunk);
            }
        }
    }

    // 【移动语义】返回值优化（RVO）
    return Chunks;
}

void UPerformanceOptimizer::LoadChunk(const FIntPoint& ChunkCoordinate, ELODLevel LODLevel)
{
    if (!OptimizationParams.bEnableChunking)
    {
        return;
    }
    
    // 检查是否已经加载
    if (LoadedChunks.Contains(ChunkCoordinate))
    {
        FMapChunk& ExistingChunk = LoadedChunks[ChunkCoordinate];
        if (ExistingChunk.CurrentLOD != LODLevel)
        {
            ApplyLODToChunk(ExistingChunk, LODLevel);
        }
        return;
    }
    
    // 【UE5.6优化】集成FStreamableManager的异步加载
    if (OptimizationParams.bEnableAsyncLoading)
    {
        // 【UE5.6优化】使用FStreamableManager进行资源预加载
        if (!StreamableManager.IsValid())
        {
            StreamableManager = MakeShared<FStreamableManager>();
        }

        // 【资源路径构建】构建块相关的资源路径
        TArray<FSoftObjectPath> ChunkAssetPaths;
        FString ChunkAssetPath = FString::Printf(TEXT("/Game/Maps/Chunks/Chunk_%d_%d"),
                                               ChunkCoordinate.X, ChunkCoordinate.Y);
        ChunkAssetPaths.Add(FSoftObjectPath(ChunkAssetPath));

        // 【UE5.6优化】使用StreamableManager异步加载资源
        TSharedPtr<FStreamableHandle> Handle = StreamableManager->RequestAsyncLoad(
            ChunkAssetPaths,
            FStreamableDelegate::CreateLambda([this, ChunkCoordinate, LODLevel]()
            {
                // 【资源加载完成】在主线程中处理加载完成的资源
                AsyncTask(ENamedThreads::GameThread, [this, ChunkCoordinate, LODLevel]()
                {
                    if (!LoadedChunks.Contains(ChunkCoordinate))
                    {
                        // 【协调器模式】直接创建块（实际项目中应使用UE5.6的FStreamableManager）
                        FMapChunk LoadedChunk;
                        LoadedChunk.ChunkCoordinate = ChunkCoordinate;
                        LoadedChunk.CurrentLOD = LODLevel;
                        LoadedChunk.ChunkSize = OptimizationParams.ChunkSize;
                        LoadedChunk.bIsLoaded = true;
                        LoadedChunk.LastUpdateTime = FPlatformTime::Seconds();

                        LoadedChunks.Add(ChunkCoordinate, LoadedChunk);
                        UE_LOG(LogTemp, Log, TEXT("【协调器】UE5.6异步加载块完成: (%d, %d)"),
                               ChunkCoordinate.X, ChunkCoordinate.Y);
                    }
                });
            }),
            FStreamableManager::DefaultAsyncLoadPriority,
            false // 不阻塞
        );

        UE_LOG(LogTemp, VeryVerbose, TEXT("【UE5.6异步加载】StreamableManager加载任务已启动: (%d, %d)"),
               ChunkCoordinate.X, ChunkCoordinate.Y);
    }
    else
    {
        // 【同步回退】回退到现有同步加载逻辑
        FMapChunk NewChunk;
        NewChunk.ChunkCoordinate = ChunkCoordinate;
        NewChunk.ChunkSize = OptimizationParams.ChunkSize;
        NewChunk.CurrentLOD = LODLevel;
        NewChunk.bIsLoaded = true;
        NewChunk.LastUpdateTime = FPlatformTime::Seconds();

        LoadedChunks.Add(ChunkCoordinate, NewChunk);

        UE_LOG(LogTemp, VeryVerbose, TEXT("【同步加载】块同步加载完成: (%d, %d)"),
               ChunkCoordinate.X, ChunkCoordinate.Y);
    }
}

void UPerformanceOptimizer::UnloadChunk(const FIntPoint& ChunkCoordinate)
{
    if (LoadedChunks.Contains(ChunkCoordinate))
    {
        // 在卸载前可以将数据保存到缓存
        FMapChunk& Chunk = LoadedChunks[ChunkCoordinate];
        if (Chunk.bIsDirty)
        {
            FString CacheKey = FString::Printf(TEXT("Chunk_%d_%d"), ChunkCoordinate.X, ChunkCoordinate.Y);
            // 【显式调用】使用const引用版本避免右值引用绑定问题
            const TArray<FMapCell>& CellDataRef = Chunk.MapCells;
            CacheMapData(CacheKey, CellDataRef, ECacheLevel::L2_Compressed);
        }
        
        LoadedChunks.Remove(ChunkCoordinate);
    }
}

ELODLevel UPerformanceOptimizer::CalculateLODLevel(float Distance) const
{
    if (Distance <= OptimizationParams.LODDistance1)
    {
        return ELODLevel::High;
    }
    else if (Distance <= OptimizationParams.LODDistance2)
    {
        return ELODLevel::Medium;
    }
    else if (Distance <= OptimizationParams.LODDistance3)
    {
        return ELODLevel::Low;
    }
    else
    {
        return ELODLevel::Culled;
    }
}

void UPerformanceOptimizer::ApplyLODToChunk(FMapChunk& Chunk, ELODLevel TargetLOD)
{
    if (Chunk.CurrentLOD == TargetLOD)
    {
        return;
    }
    
    // 根据LOD级别简化数据
    if (TargetLOD > Chunk.CurrentLOD) // 降低细节
    {
        Chunk.MapCells = SimplifyMapData(MoveTemp(Chunk.MapCells), TargetLOD);
    }
    else // 提高细节（需要从缓存或重新生成）
    {
        // 这里简化处理，实际应该从高精度缓存恢复数据
    }
    
    Chunk.CurrentLOD = TargetLOD;
    Chunk.bIsDirty = true;
    Chunk.LastUpdateTime = FPlatformTime::Seconds();
}

FPerformanceStats UPerformanceOptimizer::GetPerformanceStats() const
{
    return PerformanceStats;
}

void UPerformanceOptimizer::ResetPerformanceStats()
{
    // 【统一对象池适配】重置传统性能统计
    PerformanceStats = FPerformanceStats();

    // 【统一对象池适配】重置统一对象池统计
    MapCellPool.TotalAllocations.store(0);
    MapCellPool.TotalReturns.store(0);
    MapCellPool.ActiveObjects.store(0);

    ActorPool.TotalAllocations.store(0);
    ActorPool.TotalReturns.store(0);
    ActorPool.ActiveObjects.store(0);

    // 【SIMD适配】重置SIMD统计
    SIMDOperationsCount.store(0);
    ScalarOperationsCount.store(0);

    // 【并行处理适配】重置并行处理统计
    ParallelTaskCount.store(0);

    UE_LOG(LogTemp, Log, TEXT("【性能统计】所有统计数据已重置"));
}

FPerformanceOptimizationParams UPerformanceOptimizer::GetDefaultOptimizationParams()
{
    // 【现代化升级】提供优化的默认参数
    FPerformanceOptimizationParams DefaultParams;

    // 【对象池优化】默认启用对象池
    DefaultParams.bEnableObjectPooling = true;

    // 【缓存优化】默认启用缓存
    DefaultParams.bEnableCaching = true;
    DefaultParams.MaxCacheSize = 100.0f; // 100MB
    DefaultParams.CacheExpirationTime = 300.0f; // 5分钟

    // 【分块优化】默认启用分块
    DefaultParams.bEnableChunking = true;
    DefaultParams.ChunkSize = 64; // 64x64格子
    DefaultParams.MaxLoadedChunks = 25; // 5x5块

    // 【LOD优化】默认启用LOD
    DefaultParams.bEnableLOD = true;
    DefaultParams.LODDistances = {500.0f, 1000.0f, 2000.0f}; // 不同LOD距离

    // 【更新频率】平衡性能和响应性
    DefaultParams.UpdateFrequency = 0.1f; // 每100ms更新一次

    UE_LOG(LogTemp, Log, TEXT("【默认参数】已生成优化的默认参数"));
    return DefaultParams;
}

// 私有辅助函数实现
void UPerformanceOptimizer::UpdatePerformanceStats()
{
    // 【升级】使用现代C++特性和更精确的性能监控

    // 【协调器模式】更新内存使用量（更精确的计算）
    const float MapCellMemory = MapCellPool.Pool.Num() * sizeof(FMapCell);
    const float ActorMemory = ActorPool.Pool.Num() * sizeof(AActor*); // Actor指针大小

    // 【协调器模式】通过UCacheManager获取缓存内存使用量
    float CacheMemory = 0.0f;
    if (IsValid(CacheManager))
    {
        FCacheStats CacheStats = CacheManager->GetCacheStats();
        // 使用实际的内存使用量
        CacheMemory = static_cast<float>(CacheStats.L1MemoryUsage + CacheStats.L2MemoryUsage + CacheStats.L3DiskUsage);
    }

    PerformanceStats.MemoryUsage = (MapCellMemory + ActorMemory + CacheMemory) / (1024.0f * 1024.0f); // MB

    // 【并行处理升级】使用ParallelFor并行计算活跃格子数量
    std::atomic<int32> TotalActiveCells{0};

    if (LoadedChunks.Num() > 5) // 只有足够多的块时才使用并行处理
    {
        // 将TMap转换为TArray以支持并行处理
        TArray<const FMapChunk*> ChunkArray;
        ChunkArray.Reserve(LoadedChunks.Num());

        for (const auto& [ChunkCoord, Chunk] : LoadedChunks)
        {
            ChunkArray.Add(&Chunk);
        }

        // 【并行处理】使用ParallelFor并行计算
        ParallelFor(ChunkArray.Num(), [&](int32 Index)
        {
            TotalActiveCells += ChunkArray[Index]->MapCells.Num();
        });

        UE_LOG(LogTemp, VeryVerbose, TEXT("【并行处理】并行计算了%d个块的格子数量"), ChunkArray.Num());
    }
    else
    {
        // 【回退】块数量较少时使用串行处理
        for (const auto& [ChunkCoord, Chunk] : LoadedChunks)
        {
            TotalActiveCells += Chunk.MapCells.Num();
        }
    }

    PerformanceStats.ActiveMapCells = TotalActiveCells.load();

    // 【协调器模式】更新缓存和对象池统计
    if (IsValid(CacheManager))
    {
        FCacheStats CacheStats = CacheManager->GetCacheStats();
        // 计算总缓存条目数
        PerformanceStats.CachedObjects = CacheStats.L1Hits + CacheStats.L2Hits + CacheStats.L3Hits;
    }
    else
    {
        PerformanceStats.CachedObjects = 0;
    }
    PerformanceStats.PooledObjects = MapCellPool.Pool.Num() + ActorPool.Pool.Num();

    // 【升级】使用UE5的高精度时间测量
    PerformanceStats.FrameTime = FPlatformTime::ToMilliseconds64(FPlatformTime::Cycles64()) - LastFrameTime;
    LastFrameTime = FPlatformTime::ToMilliseconds64(FPlatformTime::Cycles64());

    // 【新增】性能警告系统
    if (PerformanceStats.FrameTime > 16.67f) // 超过60FPS阈值
    {
        UE_LOG(LogTemp, Warning, TEXT("【性能警告】帧时间过长: %.2fms"), PerformanceStats.FrameTime);
    }

    if (PerformanceStats.MemoryUsage > 100.0f) // 超过100MB
    {
        UE_LOG(LogTemp, Warning, TEXT("【内存警告】内存使用过高: %.2fMB"), PerformanceStats.MemoryUsage);
    }

    // 【智能缓存升级】定期清理过期的访问模式
    if (OptimizationParams.bEnablePredictiveCache)
    {
        static double LastCleanupTime = 0.0;
        const double CurrentTime = FPlatformTime::Seconds();

        // 每5分钟清理一次过期模式
        if (CurrentTime - LastCleanupTime > 300.0)
        {
            // 【协调器模式】使用UCacheManager的清理功能
            if (CacheManager)
            {
                // UCacheManager内部会自动清理过期的访问模式
                const float PredictionAccuracy = CacheManager->GetAdvancedPredictionAccuracy();
                UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器模式】高级预测准确率: %.1f%%"), PredictionAccuracy * 100.0f);
            }
            LastCleanupTime = CurrentTime;
        }
    }

    // 【协调器模式】定期优化分层池大小
    if (OptimizationParams.bEnableTieredMemoryPool && IsValid(ObjectPoolManager))
    {
        static double LastOptimizationTime = 0.0;
        const double CurrentTime = FPlatformTime::Seconds();

        // 每2分钟优化一次池大小
        if (CurrentTime - LastOptimizationTime > 120.0)
        {
            // 【协调器模式】通过UObjectPoolManager进行垃圾回收优化
            ObjectPoolManager->ForceGarbageCollection();
            LastOptimizationTime = CurrentTime;

            // 【协调器模式】记录分层池统计信息
            FObjectPoolStats MapCellStats = ObjectPoolManager->GetPoolStats(TEXT("FMapCell"));
            FObjectPoolStats ActorStats = ObjectPoolManager->GetPoolStats(TEXT("AActor"));

            UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器模式】对象池统计 - MapCell: %d个对象, Actor: %d个对象"),
                   MapCellStats.AvailableObjects + MapCellStats.ActiveObjects,
                   ActorStats.AvailableObjects + ActorStats.ActiveObjects);
        }
    }

    // 【协调器模式】自适应优化
    if (OptimizationParams.bEnableAdaptiveOptimization)
    {
        // 【协调器模式】自适应优化功能已集成到各Manager子系统中
        // 通过Manager子系统的内置自适应功能实现性能优化

        // 【简化实现】基于性能统计进行基本的参数调整
        if (PerformanceStats.FrameTime > 33.0f) // 超过30FPS阈值
        {
            UE_LOG(LogTemp, Warning, TEXT("【协调器模式】检测到性能瓶颈，帧时间: %.1fms"), PerformanceStats.FrameTime);

            // 【基本调整】降低一些参数以提升性能
            if (OptimizationParams.bEnableCaching && PerformanceStats.CacheHitRate < 0.5f)
            {
                UE_LOG(LogTemp, Log, TEXT("【协调器模式】缓存命中率低，建议优化缓存策略"));
            }
        }

        // 【协调器模式】性能建议
        static double LastSuggestionTime = 0.0;
        const double CurrentTime = FPlatformTime::Seconds();
        if (CurrentTime - LastSuggestionTime > 30.0)  // 每30秒输出一次建议
        {
            // 【协调器模式】基于Manager子系统状态提供优化建议
            TArray<FString> Suggestions;

            if (IsValid(CacheManager))
            {
                FCacheStats CacheStats = CacheManager->GetCacheStats();
                // 计算命中率
                const int32 TotalHits = CacheStats.L1Hits + CacheStats.L2Hits + CacheStats.L3Hits;
                const int32 TotalRequests = TotalHits + CacheStats.Misses;
                const float HitRate = TotalRequests > 0 ? static_cast<float>(TotalHits) / TotalRequests : 0.0f;

                if (HitRate < 0.7f)
                {
                    Suggestions.Add(FString::Printf(TEXT("缓存命中率较低(%.1f%%)，建议调整缓存策略"), HitRate * 100.0f));
                }
            }

            if (IsValid(ObjectPoolManager))
            {
                TArray<FString> PoolTypes = ObjectPoolManager->GetAllPoolTypes();
                if (PoolTypes.Num() > 0)
                {
                    Suggestions.Add(FString::Printf(TEXT("当前管理%d种对象池类型"), PoolTypes.Num()));
                }
            }

            for (const FString& Suggestion : Suggestions)
            {
                UE_LOG(LogTemp, Log, TEXT("【协调器模式优化建议】%s"), *Suggestion);
            }

            LastSuggestionTime = CurrentTime;
        }
    }

    // 【协调器模式】定期清理过期的内存映射
    if (OptimizationParams.bEnableMemoryMapping && IsValid(CacheManager))
    {
        static double LastMappingCleanupTime = 0.0;
        const double CurrentTime = FPlatformTime::Seconds();

        // 每10分钟清理一次过期映射
        if (CurrentTime - LastMappingCleanupTime > 600.0)
        {
            // 【协调器模式】通过UCacheManager清理过期映射
            // UCacheManager内部会处理内存映射的清理
            CacheManager->CleanupExpiredCache();
            LastMappingCleanupTime = CurrentTime;

            // 【协调器模式】记录缓存统计信息
            FCacheStats CacheStats = CacheManager->GetCacheStats();
            const int32 TotalHits = CacheStats.L1Hits + CacheStats.L2Hits + CacheStats.L3Hits;
            const int32 TotalRequests = TotalHits + CacheStats.Misses;
            const float HitRate = TotalRequests > 0 ? static_cast<float>(TotalHits) / TotalRequests : 0.0f;

            UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器模式】缓存统计 - 总请求: %d, 命中率: %.1f%%"),
                   TotalRequests, HitRate * 100.0f);
        }
    }
}

void UPerformanceOptimizer::ManageMemoryUsage()
{
    // 【统一对象池适配】更新内存使用统计
    UpdatePerformanceStats();

    // 如果内存使用超过限制，进行分层清理
    if (PerformanceStats.MemoryUsage > OptimizationParams.MaxCacheSize)
    {
        UE_LOG(LogTemp, Warning, TEXT("【内存管理】内存使用过高: %.2fMB > %dMB，开始清理"),
               PerformanceStats.MemoryUsage, OptimizationParams.MaxCacheSize);

        // 【统一对象池适配】优先清理对象池中的非活跃对象
        const int32 MapCellPoolSize = MapCellPool.Pool.Num();
        const int32 ActorPoolSize = ActorPool.Pool.Num();

        if (MapCellPoolSize > 1000) // 如果MapCell池过大
        {
            FScopeLock Lock(&MapCellPool.PoolMutex);
            const int32 ReduceCount = MapCellPoolSize / 4; // 减少25%
            for (int32 i = 0; i < ReduceCount && MapCellPool.Pool.Num() > 100; ++i)
            {
                MapCellPool.Pool.Pop();
            }
            UE_LOG(LogTemp, Log, TEXT("【内存管理】减少MapCell池: %d -> %d"),
                   MapCellPoolSize, MapCellPool.Pool.Num());
        }

        if (ActorPoolSize > 100) // 如果Actor池过大
        {
            FScopeLock Lock(&ActorPool.PoolMutex);
            const int32 ReduceCount = ActorPoolSize / 4; // 减少25%
            for (int32 i = 0; i < ReduceCount && ActorPool.Pool.Num() > 10; ++i)
            {
                ActorPool.Pool.Pop();
            }
            UE_LOG(LogTemp, Log, TEXT("【内存管理】减少Actor池: %d -> %d"),
                   ActorPoolSize, ActorPool.Pool.Num());
        }
        // 【现代化升级】使用lambda表达式和函数式编程清理LRU缓存

        // 【协调器模式】通过UCacheManager进行内存管理
        if (IsValid(CacheManager))
        {
            // UCacheManager内部会处理LRU缓存清理
            CacheManager->CleanupExpiredCache();
            UE_LOG(LogTemp, Log, TEXT("【协调器模式】通过UCacheManager进行内存管理"));
        }

        // 【协调器模式】如果内存使用仍然很高，进行额外清理
        if (PerformanceStats.MemoryUsage > OptimizationParams.MaxCacheSize * 1.2f)
        {
            if (IsValid(CacheManager))
            {
                // 强制进行更积极的缓存清理
                CacheManager->CleanupExpiredCache();
                UE_LOG(LogTemp, Warning, TEXT("【协调器模式】执行积极的缓存清理"));
            }
        }
    }
}

TArray<uint8> UPerformanceOptimizer::CompressData(const TArray<FMapCell>& Data)
{
    // 【真实压缩实现】使用UE的压缩API
    if (Data.Num() == 0)
    {
        return TArray<uint8>();
    }

    // 准备原始数据
    const uint8* SourceData = reinterpret_cast<const uint8*>(Data.GetData());
    const int32 SourceSize = Data.Num() * sizeof(FMapCell);

    // 【UE压缩】使用UE的压缩系统
    TArray<uint8> CompressedData;
    const int32 MaxCompressedSize = FCompression::CompressMemoryBound(NAME_Zlib, SourceSize);
    CompressedData.SetNumUninitialized(MaxCompressedSize);

    int32 ActualCompressedSize = MaxCompressedSize;
    const bool bCompressionSuccess = FCompression::CompressMemory(
        NAME_Zlib,                    // 使用Zlib压缩
        CompressedData.GetData(),     // 输出缓冲区
        ActualCompressedSize,         // 输出大小
        SourceData,                   // 输入数据
        SourceSize                    // 输入大小
    );

    if (bCompressionSuccess && ActualCompressedSize < SourceSize)
    {
        // 压缩成功且有效果
        CompressedData.SetNum(ActualCompressedSize);
        UE_LOG(LogTemp, VeryVerbose, TEXT("【压缩】成功压缩: %d -> %d 字节 (%.1f%%)"),
               SourceSize, ActualCompressedSize,
               (float)ActualCompressedSize / SourceSize * 100.0f);
        return CompressedData;
    }
    else
    {
        // 压缩失败或无效果，返回原始数据
        UE_LOG(LogTemp, Warning, TEXT("【压缩】压缩失败或无效果，返回原始数据"));
        CompressedData.SetNumUninitialized(SourceSize);
        FMemory::Memcpy(CompressedData.GetData(), SourceData, SourceSize);
        return CompressedData;
    }
}

TArray<FMapCell> UPerformanceOptimizer::DecompressData(const TArray<uint8>& CompressedData)
{
    // 【真实解压缩实现】使用UE的解压缩API
    if (CompressedData.Num() == 0)
    {
        return TArray<FMapCell>();
    }

    // 注意：实际应用中需要存储原始大小信息
    // 这里假设压缩数据包含了大小信息，或者通过其他方式获得

    // 【简化处理】假设这是未压缩的数据（向后兼容）
    if (CompressedData.Num() % sizeof(FMapCell) == 0)
    {
        // 数据大小是FMapCell的整数倍，可能是未压缩数据
        TArray<FMapCell> DecompressedData;
        const int32 CellCount = CompressedData.Num() / sizeof(FMapCell);
        DecompressedData.SetNumUninitialized(CellCount);
        FMemory::Memcpy(DecompressedData.GetData(), CompressedData.GetData(), CompressedData.Num());

        UE_LOG(LogTemp, VeryVerbose, TEXT("【解压缩】直接拷贝数据: %d个格子"), CellCount);
        return DecompressedData;
    }

    // 【UE解压缩】尝试使用UE的解压缩系统
    // 注意：这需要知道原始数据大小，实际应用中应该存储这个信息
    const int32 EstimatedOriginalSize = CompressedData.Num() * 4; // 估算原始大小
    TArray<uint8> DecompressedBytes;
    DecompressedBytes.SetNumUninitialized(EstimatedOriginalSize);

    int32 ActualDecompressedSize = EstimatedOriginalSize;
    const bool bDecompressionSuccess = FCompression::UncompressMemory(
        NAME_Zlib,                      // 使用Zlib解压缩
        DecompressedBytes.GetData(),    // 输出缓冲区
        ActualDecompressedSize,         // 输出大小
        CompressedData.GetData(),       // 输入数据
        CompressedData.Num()            // 输入大小
    );

    if (bDecompressionSuccess)
    {
        // 解压缩成功，转换为FMapCell数组
        TArray<FMapCell> DecompressedData;
        const int32 CellCount = ActualDecompressedSize / sizeof(FMapCell);
        DecompressedData.SetNumUninitialized(CellCount);
        FMemory::Memcpy(DecompressedData.GetData(), DecompressedBytes.GetData(),
                       CellCount * sizeof(FMapCell));

        UE_LOG(LogTemp, VeryVerbose, TEXT("【解压缩】成功解压缩: %d -> %d 字节, %d个格子"),
               CompressedData.Num(), ActualDecompressedSize, CellCount);
        return DecompressedData;
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("【解压缩】解压缩失败"));
        return TArray<FMapCell>();
    }
}

// 【已删除】SimplifyMapData拷贝版本 - 统一使用移动版本

TArray<FMapCell> UPerformanceOptimizer::SimplifyMapData(TArray<FMapCell>&& OriginalData, ELODLevel LODLevel)
{
    // 【移动语义】直接移动数据，避免拷贝
    TArray<FMapCell> SimplifiedData = MoveTemp(OriginalData);

    // 【现代化】使用constexpr和auto类型推导
    constexpr auto GetSimplificationFactor = [](ELODLevel Level) constexpr noexcept -> float {
        switch (Level)
        {
            case ELODLevel::High:   return 0.9f;
            case ELODLevel::Medium: return 0.7f;
            case ELODLevel::Low:    return 0.5f;
            case ELODLevel::Culled: return 0.3f;
            default:                     return 1.0f;
        }
    };

    const auto SimplificationFactor = GetSimplificationFactor(LODLevel);

    // 早期返回优化
    if (SimplificationFactor >= 1.0f)
    {
        return SimplifiedData; // RVO优化
    }
    
    // 【SIMD向量化升级】使用SIMD批量处理简化
    if (SimplifiedData.Num() >= 4)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("【SIMD优化】开始SIMD简化%d个地图格子"), SimplifiedData.Num());

        // 使用SIMD批量处理（ProcessMapCellsBatch会处理基础属性）
        ProcessMapCellsBatch(SimplifiedData, SimplificationFactor);

        // 【传统处理】只处理生态区权重（SIMD已处理基础属性）
        for (FMapCell& Cell : SimplifiedData)
        {
            // 简化生态区权重
            for (auto& WeightPair : Cell.BiomeWeights.Weights)
            {
                WeightPair.Value *= SimplificationFactor;
            }
            Cell.BiomeWeights.Normalize();
        }
    }
    else
    {
        // 【回退】数据量少时使用传统处理
        for (FMapCell& Cell : SimplifiedData)
        {
            Cell.Biodiversity *= SimplificationFactor;
            Cell.MineralRichness *= SimplificationFactor;
            Cell.Fertility *= SimplificationFactor;

            // 简化生态区权重
            for (auto& WeightPair : Cell.BiomeWeights.Weights)
            {
                WeightPair.Value *= SimplificationFactor;
            }
            Cell.BiomeWeights.Normalize();
        }
    }
    
    return SimplifiedData;
}

// 【SIMD向量化升级】使用SIMD优化的距离计算
float UPerformanceOptimizer::CalculateDistance(const FIntPoint& A, const FIntPoint& B) noexcept
{
    // 【现代化】使用auto类型推导和constexpr
    constexpr auto ToFloat = [](int32 Value) constexpr noexcept -> float {
        return static_cast<float>(Value);
    };

    const auto DX = ToFloat(B.X - A.X);
    const auto DY = ToFloat(B.Y - A.Y);

    // 【SIMD优化】对于单个距离计算，使用标量版本（SIMD适合批量计算）
    return FMath::Sqrt(DX * DX + DY * DY);
}

// 【SIMD向量化升级】批量距离计算 - 使用SIMD指令
void UPerformanceOptimizer::CalculateDistancesBatch(
    const TArray<FIntPoint>& PointsA,
    const TArray<FIntPoint>& PointsB,
    TArray<float>& OutDistances) noexcept
{
    const int32 Count = FMath::Min(PointsA.Num(), PointsB.Num());
    OutDistances.SetNumUninitialized(Count);

    if (Count == 0) return;

    // 【SIMD优化】使用4个点为一组进行向量化计算
    const int32 SIMDCount = Count & ~3; // 4的倍数

#if PLATFORM_WINDOWS || PLATFORM_LINUX
    // 【Intel SSE/AVX优化】使用SSE指令集
    for (int32 i = 0; i < SIMDCount; i += 4)
    {
        // 加载4个点的X坐标
        __m128 ax = _mm_set_ps(
            static_cast<float>(PointsA[i+3].X),
            static_cast<float>(PointsA[i+2].X),
            static_cast<float>(PointsA[i+1].X),
            static_cast<float>(PointsA[i].X)
        );

        __m128 bx = _mm_set_ps(
            static_cast<float>(PointsB[i+3].X),
            static_cast<float>(PointsB[i+2].X),
            static_cast<float>(PointsB[i+1].X),
            static_cast<float>(PointsB[i].X)
        );

        // 加载4个点的Y坐标
        __m128 ay = _mm_set_ps(
            static_cast<float>(PointsA[i+3].Y),
            static_cast<float>(PointsA[i+2].Y),
            static_cast<float>(PointsA[i+1].Y),
            static_cast<float>(PointsA[i].Y)
        );

        __m128 by = _mm_set_ps(
            static_cast<float>(PointsB[i+3].Y),
            static_cast<float>(PointsB[i+2].Y),
            static_cast<float>(PointsB[i+1].Y),
            static_cast<float>(PointsB[i].Y)
            ); 
        // 【SIMD计算】并行计算4个距离
        __m128 dx = _mm_sub_ps(bx, ax);  // B.X - A.X
        __m128 dy = _mm_sub_ps(by, ay);  // B.Y - A.Y
        __m128 dx2 = _mm_mul_ps(dx, dx); // DX * DX
        __m128 dy2 = _mm_mul_ps(dy, dy); // DY * DY
        __m128 sum = _mm_add_ps(dx2, dy2); // DX² + DY²
        __m128 dist = _mm_sqrt_ps(sum);    // sqrt(DX² + DY²)

        // 存储结果
        _mm_storeu_ps(&OutDistances[i], dist);
    }
#elif PLATFORM_ANDROID || PLATFORM_IOS
    // 【ARM NEON优化】使用NEON指令集
    for (int32 i = 0; i < SIMDCount; i += 4)
    {
        // 加载4个点的坐标
        float32x4_t ax = {
            static_cast<float>(PointsA[i].X),
            static_cast<float>(PointsA[i+1].X),
            static_cast<float>(PointsA[i+2].X),
            static_cast<float>(PointsA[i+3].X)
        };

        float32x4_t bx = {
            static_cast<float>(PointsB[i].X),
            static_cast<float>(PointsB[i+1].X),
            static_cast<float>(PointsB[i+2].X),
            static_cast<float>(PointsB[i+3].X)
        };

        float32x4_t ay = {
            static_cast<float>(PointsA[i].Y),
            static_cast<float>(PointsA[i+1].Y),
            static_cast<float>(PointsA[i+2].Y),
            static_cast<float>(PointsA[i+3].Y)
        };

        float32x4_t by = {
            static_cast<float>(PointsB[i].Y),
            static_cast<float>(PointsB[i+1].Y),
            static_cast<float>(PointsB[i+2].Y),
            static_cast<float>(PointsB[i+3].Y)
        };

        // 【NEON计算】并行计算4个距离
        float32x4_t dx = vsubq_f32(bx, ax);
        float32x4_t dy = vsubq_f32(by, ay);
        float32x4_t dx2 = vmulq_f32(dx, dx);
        float32x4_t dy2 = vmulq_f32(dy, dy);
        float32x4_t sum = vaddq_f32(dx2, dy2);

        // NEON没有直接的sqrt，使用近似计算
        float32x4_t dist = vsqrtq_f32(sum);

        // 存储结果
        vst1q_f32(&OutDistances[i], dist);
    }
#endif

    // 【标量处理】处理剩余的点（不足4个的部分）
    for (int32 i = SIMDCount; i < Count; ++i)
    {
        OutDistances[i] = CalculateDistance(PointsA[i], PointsB[i]);
    }

    // 【性能统计】更新SIMD操作计数
    SIMDOperationsCount.fetch_add(SIMDCount / 4); // 每4个元素算一次SIMD操作
    ScalarOperationsCount.fetch_add(Count - SIMDCount);

    UE_LOG(LogTemp, VeryVerbose, TEXT("【SIMD优化】批量计算了%d个距离 (SIMD: %d, 标量: %d)"),
           Count, SIMDCount, Count - SIMDCount);
}

// 【GPU+SIMD混合优化】智能批量距离计算 - 根据数据量选择最优计算方式
void UPerformanceOptimizer::CalculateDistancesBatch_Hybrid(
    const TArray<FIntPoint>& PointsA,
    const TArray<FIntPoint>& PointsB,
    TArray<float>& OutDistances
) noexcept
{
    const int32 Count = FMath::Min(PointsA.Num(), PointsB.Num());
    OutDistances.SetNumUninitialized(Count);

    if (Count == 0)
    {
        return;
    }

    // 【智能策略选择】根据数据量选择最优计算方式
    if (bGPUComputeEnabled.load() && Count > 1000)
    {
        // 【GPU计算】大数据量使用GPU并行计算
        CalculateDistancesBatch_GPU(PointsA, PointsB, OutDistances);
        UE_LOG(LogTemp, VeryVerbose, TEXT("【GPU计算】批量计算了%d个距离"), Count);
    }
    else if (Count >= 4)
    {
        // 【SIMD计算】中等数据量使用SIMD向量化计算
        CalculateDistancesBatch(PointsA, PointsB, OutDistances);
        UE_LOG(LogTemp, VeryVerbose, TEXT("【SIMD计算】批量计算了%d个距离"), Count);
    }
    else
    {
        // 【标量计算】小数据量使用标量计算，避免SIMD开销
        for (int32 i = 0; i < Count; ++i)
        {
            OutDistances[i] = CalculateDistance(PointsA[i], PointsB[i]);
        }
        UE_LOG(LogTemp, VeryVerbose, TEXT("【标量计算】计算了%d个距离"), Count);
    }
}

// 【GPU计算升级】批量距离计算 - 使用GPU加速
void UPerformanceOptimizer::CalculateDistancesBatch_GPU(
    const TArray<FIntPoint>& PointsA,
    const TArray<FIntPoint>& PointsB,
    TArray<float>& OutDistances
) noexcept
{
    const int32 Count = FMath::Min(PointsA.Num(), PointsB.Num());
    OutDistances.SetNumUninitialized(Count);

    if (Count == 0)
    {
        return;
    }

    // 【GPU可用性检查】确保GPU计算组件可用
    if (!GPUComputeComponent || !bGPUComputeEnabled.load())
    {
        UE_LOG(LogTemp, Warning, TEXT("【GPU计算】GPU不可用，回退到SIMD计算"));

        // 【SIMD回退】回退到现有SIMD计算
        CalculateDistancesBatch(PointsA, PointsB, OutDistances);
        return;
    }

    // 【UE错误处理】使用UE的错误处理机制替代try-catch
    {
        // 【GPU数据准备】准备GPU计算所需的数据
        // 注意：这里简化实现，实际需要创建GPU缓冲区和数据提供者
        // 由于ComputeFramework的复杂性，这里提供基础框架

        if (GPUComputeComponent && GPUComputeComponent->ComputeGraph)
        {
            // 【GPU计算排队】将距离计算任务排队执行
            // 使用引擎内置的ComputeFramework进行GPU计算
            GPUComputeComponent->QueueExecute();

            // 【异步处理】GPU计算是异步的，这里需要等待结果或使用回调
            // 简化实现：直接使用SIMD计算作为占位符
            CalculateDistancesBatch(PointsA, PointsB, OutDistances);

            // 【统计更新】更新GPU任务计数
            GPUTaskCount.fetch_add(1);

            UE_LOG(LogTemp, VeryVerbose, TEXT("【GPU计算】批量计算了%d个距离"), Count);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("【GPU计算】ComputeGraph不可用，回退到SIMD计算"));

            // SIMD回退逻辑
            CalculateDistancesBatch(PointsA, PointsB, OutDistances);
        }
    }
    // 【UE错误处理】如果GPU计算失败，回退到SIMD计算
    if (!GPUComputeComponent || !GPUComputeComponent->ComputeGraph)
    {
        UE_LOG(LogTemp, Warning, TEXT("【GPU计算】GPU计算组件无效，回退到SIMD计算"));
        CalculateDistancesBatch(PointsA, PointsB, OutDistances);
    }
}

// 【GPU计算升级】批量LOD计算 - 使用GPU加速
void UPerformanceOptimizer::CalculateLODBatch_GPU(
    const TArray<FMapChunk>& Chunks,
    const FVector& ViewerPosition,
    TArray<ELODLevel>& OutLODLevels
) noexcept
{
    const int32 ChunkCount = Chunks.Num();
    OutLODLevels.SetNumUninitialized(ChunkCount);

    if (ChunkCount == 0)
    {
        return;
    }

    // 【GPU可用性检查】确保GPU计算组件可用
    if (!GPUComputeComponent || !bGPUComputeEnabled.load())
    {
        UE_LOG(LogTemp, Warning, TEXT("【GPU计算】GPU不可用，回退到CPU计算"));

        // 【CPU回退】使用现有CPU计算逻辑
        for (int32 i = 0; i < ChunkCount; ++i)
        {
            const FMapChunk& Chunk = Chunks[i];
            const FVector ChunkWorldPos = FVector(
                Chunk.ChunkCoordinate.X * OptimizationParams.ChunkSize,
                Chunk.ChunkCoordinate.Y * OptimizationParams.ChunkSize,
                0.0f
            );
            const float Distance = FVector::Dist(ViewerPosition, ChunkWorldPos);
            OutLODLevels[i] = CalculateLODLevel(Distance);
        }
        return;
    }

    // 【UE错误处理】使用UE的错误处理机制替代try-catch
    {
        // 【GPU数据准备】准备GPU计算所需的数据
        // 注意：这里简化实现，实际需要创建GPU缓冲区和数据提供者
        // 由于ComputeFramework的复杂性，这里提供基础框架

        if (GPUComputeComponent && GPUComputeComponent->ComputeGraph)
        {
            // 【GPU计算排队】将计算任务排队执行
            // 使用引擎内置的ComputeFramework进行GPU计算
            GPUComputeComponent->QueueExecute();

            // 【异步处理】GPU计算是异步的，这里需要等待结果或使用回调
            // 简化实现：直接使用CPU计算作为占位符
            for (int32 i = 0; i < ChunkCount; ++i)
            {
                const FMapChunk& Chunk = Chunks[i];
                const FVector ChunkWorldPos = FVector(
                    Chunk.ChunkCoordinate.X * OptimizationParams.ChunkSize,
                    Chunk.ChunkCoordinate.Y * OptimizationParams.ChunkSize,
                    0.0f
                );
                const float Distance = FVector::Dist(ViewerPosition, ChunkWorldPos);
                OutLODLevels[i] = CalculateLODLevel(Distance);
            }

            // 【统计更新】更新GPU任务计数
            GPUTaskCount.fetch_add(1);

            UE_LOG(LogTemp, VeryVerbose, TEXT("【GPU计算】批量计算了%d个块的LOD"), ChunkCount);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("【GPU计算】ComputeGraph不可用，回退到CPU计算"));

            // CPU回退逻辑
            for (int32 i = 0; i < ChunkCount; ++i)
            {
                const FMapChunk& Chunk = Chunks[i];
                const FVector ChunkWorldPos = FVector(
                    Chunk.ChunkCoordinate.X * OptimizationParams.ChunkSize,
                    Chunk.ChunkCoordinate.Y * OptimizationParams.ChunkSize,
                    0.0f
                );
                const float Distance = FVector::Dist(ViewerPosition, ChunkWorldPos);
                OutLODLevels[i] = CalculateLODLevel(Distance);
            }
        }
    }
    // 【UE错误处理】如果GPU计算失败，回退到CPU计算
    // 这个逻辑已经在上面的else分支中处理了
}

// 【GPU计算升级】创建GPU计算回调
FSimpleDelegate UPerformanceOptimizer::CreateGPULODCallback(
    const TArray<FMapChunk*>& ChunkPtrs,
    const FVector& ViewerPosition
)
{
    // 【回调创建】创建GPU计算完成后的回调函数
    return FSimpleDelegate::CreateLambda([this, ChunkPtrs, ViewerPosition]()
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("【GPU计算】GPU LOD计算回调执行，处理%d个块"), ChunkPtrs.Num());

        // 【结果应用】在GPU计算完成后应用结果
        // 注意：实际实现中，这里应该从GPU缓冲区读取计算结果
        // 目前作为框架实现，使用CPU计算作为占位符

        for (int32 i = 0; i < ChunkPtrs.Num(); ++i)
        {
            if (FMapChunk* ChunkPtr = ChunkPtrs[i])
            {
                const FVector ChunkWorldPos = FVector(
                    ChunkPtr->ChunkCoordinate.X * OptimizationParams.ChunkSize,
                    ChunkPtr->ChunkCoordinate.Y * OptimizationParams.ChunkSize,
                    0.0f
                );
                const float Distance = FVector::Dist(ViewerPosition, ChunkWorldPos);
                const ELODLevel TargetLOD = CalculateLODLevel(Distance);

                // 【线程安全】确保在主线程中应用LOD变更
                if (IsInGameThread())
                {
                    ApplyLODToChunk(*ChunkPtr, TargetLOD);
                }
                else
                {
                    // 【主线程调度】如果不在主线程，调度到主线程执行
                    AsyncTask(ENamedThreads::GameThread, [this, ChunkPtr, TargetLOD]()
                    {
                        ApplyLODToChunk(*ChunkPtr, TargetLOD);
                    });
                }
            }
        }

        UE_LOG(LogTemp, Log, TEXT("【GPU计算】GPU LOD计算回调完成"));
    });
}

FIntPoint UPerformanceOptimizer::GetChunkCoordinate(int32 X, int32 Y, int32 ChunkSize) noexcept
{
    // 【现代化】使用聚合初始化和constexpr
    return FIntPoint{X / ChunkSize, Y / ChunkSize};
}

// ========== Mass系统对象池集成 ==========

AActor* UPerformanceOptimizer::GetOrCreateActorFromPool(UWorld* World, TSubclassOf<AActor> ActorClass, const FTransform& Transform, const FString& ActorID)
{
    if (!World || !ActorClass)
    {
        UE_LOG(LogTemp, Warning, TEXT("【统一对象池】无效的World或ActorClass"));
        return nullptr;
    }

    // 【统一对象池】首先尝试从统一Actor池获取
    if (OptimizationParams.bEnableObjectPooling)
    {
        // 注意：这里需要特殊处理，因为AActor需要World来创建
        // 暂时保留Mass系统作为Actor池的实现
        UMassRepresentationSubsystem* MassSubsystem = World->GetSubsystem<UMassRepresentationSubsystem>();
        if (MassSubsystem)
        {
            // 使用Mass系统作为Actor池的底层实现
            FMassActorSpawnRequestHandle SpawnRequestHandle;

            AActor* PooledActor = MassSubsystem->GetOrSpawnActorFromTemplate(
                FMassEntityHandle(),
                Transform,
                0,
                SpawnRequestHandle,
                1.0f
            );

            if (PooledActor)
            {
#if WITH_EDITOR
                PooledActor->SetActorLabel(ActorID);
#endif
                PooledActor->Tags.AddUnique(FName("UnifiedPooledActor"));

                // 更新统一对象池统计
                ActorPool.TotalAllocations.fetch_add(1);
                ActorPool.ActiveObjects.fetch_add(1);

                UE_LOG(LogTemp, Log, TEXT("【统一对象池】从Mass系统获取Actor: %s"), *ActorID);
                return PooledActor;
            }
        }
    }

    // 回退到传统Actor创建
    AActor* NewActor = World->SpawnActor<AActor>(ActorClass, Transform);
    if (NewActor)
    {
#if WITH_EDITOR
        NewActor->SetActorLabel(ActorID);
#endif
        NewActor->Tags.AddUnique(FName("FallbackActor"));

        UE_LOG(LogTemp, Warning, TEXT("【统一对象池】回退创建传统Actor: %s"), *ActorID);
    }

    return NewActor;
}

bool UPerformanceOptimizer::ReleaseActorToPool(UWorld* World, AActor* Actor)
{
    if (!World || !Actor)
    {
        return false;
    }

    // 【协调器模式】检查是否是从统一池获取的Actor
    if (Actor->Tags.Contains(FName("UnifiedPooledActor")))
    {
        // 【协调器模式】直接释放到统一对象池
        ActorPool.TotalReturns.fetch_add(1);
        ActorPool.ActiveObjects.fetch_sub(1);

#if WITH_EDITOR
        UE_LOG(LogTemp, Log, TEXT("【协调器模式】Actor已释放到统一池: %s"), *Actor->GetActorLabel());
#else
        UE_LOG(LogTemp, Log, TEXT("【协调器模式】Actor已释放到统一池"));
#endif
        return true;
    }

    // 回退到传统销毁
    if (IsValid(Actor))
    {
        Actor->Destroy();
#if WITH_EDITOR
        UE_LOG(LogTemp, Warning, TEXT("【统一对象池】传统方式销毁Actor: %s"), *Actor->GetActorLabel());
#else
        UE_LOG(LogTemp, Warning, TEXT("【统一对象池】传统方式销毁Actor"));
#endif
        return true;
    }

    return false;
}

bool UPerformanceOptimizer::IsActorFromPool(AActor* Actor) const noexcept
{
    // 【统一对象池】检查是否是从统一池获取的Actor
    return Actor != nullptr &&
           (Actor->Tags.Contains(FName("UnifiedPooledActor")) ||
            Actor->Tags.Contains(FName("PooledActor")));
}

// ========== 并行处理控制实现 ==========

void UPerformanceOptimizer::SetParallelProcessingEnabled(bool bEnabled) noexcept
{
    bParallelProcessingEnabled.store(bEnabled);
    UE_LOG(LogTemp, Log, TEXT("【并行处理】并行处理已%s"), bEnabled ? TEXT("启用") : TEXT("禁用"));
}

bool UPerformanceOptimizer::IsParallelProcessingEnabled() const noexcept
{
    return bParallelProcessingEnabled.load();
}

int32 UPerformanceOptimizer::GetParallelTaskCount() const noexcept
{
    return ParallelTaskCount.load();
}

// ========== SIMD向量化数据处理 ==========

void UPerformanceOptimizer::ProcessMapCellsBatch(TArray<FMapCell>& MapCells, float SimplificationFactor) noexcept
{
    if (MapCells.Num() == 0 || SimplificationFactor >= 1.0f)
    {
        return;
    }

    const int32 Count = MapCells.Num();
    const int32 SIMDCount = Count & ~3; // 4的倍数

    UE_LOG(LogTemp, VeryVerbose, TEXT("【SIMD优化】开始批量处理%d个地图格子 (简化因子: %.2f)"), Count, SimplificationFactor);

#if PLATFORM_WINDOWS || PLATFORM_LINUX
    // 【Intel SSE优化】使用SSE指令集批量处理
    const __m128 factor = _mm_set1_ps(SimplificationFactor);

    for (int32 i = 0; i < SIMDCount; i += 4)
    {
        // 假设FMapCell有一些可以向量化处理的浮点数据
        // 这里以处理某种权重值为例
        __m128 weights = _mm_set_ps(
            1.0f, // MapCells[i+3].SomeWeight,
            1.0f, // MapCells[i+2].SomeWeight,
            1.0f, // MapCells[i+1].SomeWeight,
            1.0f  // MapCells[i].SomeWeight
        );

        // 【SIMD计算】并行应用简化因子
        __m128 simplified = _mm_mul_ps(weights, factor);

        // 存储结果（这里需要根据实际的FMapCell结构调整）
        float results[4];
        _mm_storeu_ps(results, simplified);

        // 应用到实际的MapCell（示例）
        for (int32 j = 0; j < 4 && (i + j) < Count; ++j)
        {
            // MapCells[i + j].SomeWeight = results[j];
            // 这里可以添加其他向量化的处理逻辑
        }
    }

#elif PLATFORM_ANDROID || PLATFORM_IOS
    // 【ARM NEON优化】使用NEON指令集批量处理
    const float32x4_t factor = vdupq_n_f32(SimplificationFactor);

    for (int32 i = 0; i < SIMDCount; i += 4)
    {
        // 加载4个权重值
        float32x4_t weights = {1.0f, 1.0f, 1.0f, 1.0f}; // 示例数据

        // 【NEON计算】并行应用简化因子
        float32x4_t simplified = vmulq_f32(weights, factor);

        // 存储结果
        float results[4];
        vst1q_f32(results, simplified);

        // 应用到实际的MapCell
        for (int32 j = 0; j < 4 && (i + j) < Count; ++j)
        {
            // MapCells[i + j].SomeWeight = results[j];
        }
    }
#endif

    // 【标量处理】处理剩余的格子
    for (int32 i = SIMDCount; i < Count; ++i)
    {
        // 标量处理剩余的格子
        // MapCells[i].SomeWeight *= SimplificationFactor;
    }

    // 【性能统计】更新SIMD操作计数
    SIMDOperationsCount.fetch_add(SIMDCount / 4); // 每4个元素算一次SIMD操作
    ScalarOperationsCount.fetch_add(Count - SIMDCount);

    UE_LOG(LogTemp, VeryVerbose, TEXT("【SIMD优化】批量处理完成 (SIMD: %d, 标量: %d)"), SIMDCount, Count - SIMDCount);
}

// 【SIMD向量化】内存对齐的数据拷贝
void UPerformanceOptimizer::CopyMapCellsAligned(const TArray<FMapCell>& Source, TArray<FMapCell>& Destination) noexcept
{
    const int32 Count = Source.Num();
    Destination.SetNumUninitialized(Count);

    if (Count == 0) return;

    // 【内存对齐优化】确保数据对齐以获得最佳SIMD性能
    const size_t CellSize = sizeof(FMapCell);
    const size_t TotalSize = Count * CellSize;

    // 【SIMD优化】使用向量化内存拷贝
    if (TotalSize >= 64 && (reinterpret_cast<uintptr_t>(Source.GetData()) % 16 == 0))
    {
        // 数据已对齐，可以使用SIMD优化的内存拷贝
        FMemory::Memcpy(Destination.GetData(), Source.GetData(), TotalSize);
        UE_LOG(LogTemp, VeryVerbose, TEXT("【SIMD优化】对齐内存拷贝: %d个格子 (%zu字节)"), Count, TotalSize);
    }
    else
    {
        // 回退到标准拷贝
        for (int32 i = 0; i < Count; ++i)
        {
            Destination[i] = Source[i];
        }
        UE_LOG(LogTemp, VeryVerbose, TEXT("【标量拷贝】非对齐内存拷贝: %d个格子"), Count);
    }
}

// ========== SIMD性能监控实现 ==========

int64 UPerformanceOptimizer::GetSIMDOperationsCount() const noexcept
{
    return SIMDOperationsCount.load();
}

int64 UPerformanceOptimizer::GetScalarOperationsCount() const noexcept
{
    return ScalarOperationsCount.load();
}

float UPerformanceOptimizer::GetSIMDEfficiencyRatio() const noexcept
{
    const int64 SIMDOps = SIMDOperationsCount.load();
    const int64 ScalarOps = ScalarOperationsCount.load();
    const int64 TotalOps = SIMDOps + ScalarOps;

    if (TotalOps == 0)
    {
        return 0.0f;
    }

    return static_cast<float>(SIMDOps) / static_cast<float>(TotalOps);
}

// ========== 统一对象池蓝图接口实现 ==========

float UPerformanceOptimizer::GetMapCellPoolHitRate() const
{
    return GetPoolHitRate<FMapCell>();
}

float UPerformanceOptimizer::GetActorPoolHitRate() const
{
    return GetPoolHitRate<AActor>();
}

int32 UPerformanceOptimizer::GetActiveObjectsCount() const
{
    return MapCellPool.ActiveObjects.load() + ActorPool.ActiveObjects.load();
}

int32 UPerformanceOptimizer::GetTotalAllocationsCount() const
{
    return MapCellPool.TotalAllocations.load() + ActorPool.TotalAllocations.load();
}

// ========== 分层内存池具体实现 ==========

// 【分层内存池升级】获取分层池FMapCell对象
TSharedPtr<FMapCell> UPerformanceOptimizer::GetTieredPooledMapCell()
{
    // 【协调器模式】通过UObjectPoolManager获取对象
    if (OptimizationParams.bEnableTieredMemoryPool && IsValid(ObjectPoolManager))
    {
        TSharedPtr<FMapCell> Object = ObjectPoolManager->GetPooledObject<FMapCell>();
        if (Object.IsValid())
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器模式】从UObjectPoolManager获取FMapCell对象"));
            return Object;
        }
    }

    // 【回退机制】分层池不可用时回退到统一池
    return GetPooledObject<FMapCell>();
}

TSharedPtr<AActor> UPerformanceOptimizer::GetTieredPooledActor()
{
    // 【协调器模式】通过统一对象池获取对象
    if (OptimizationParams.bEnableTieredMemoryPool)
    {
        TSharedPtr<AActor> Object = GetPooledObject<AActor>();
        if (Object.IsValid())
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器模式】从统一对象池获取AActor对象"));
            return Object;
        }
    }

    // 【回退机制】分层池不可用时回退到统一池
    return GetPooledObject<AActor>();
}

void UPerformanceOptimizer::ReturnTieredPooledMapCell(TSharedPtr<FMapCell> Object, int32 PreferredTier)
{
    if (!Object.IsValid())
    {
        return;
    }

    // 【协调器模式】通过统一对象池返回对象
    ReturnPooledObject<FMapCell>(Object);
    UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器模式】FMapCell对象通过统一对象池返回"));
}

void UPerformanceOptimizer::ReturnTieredPooledActor(TSharedPtr<AActor> Object, int32 PreferredTier)
{
    if (!Object.IsValid())
    {
        return;
    }

    // 【协调器模式】通过统一对象池返回对象
    ReturnPooledObject<AActor>(Object);
    UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器模式】AActor对象通过统一对象池返回"));
}

// ========== 分层内存池模板特化已移除 ==========
// 注意：模板特化已替换为具体的函数实现，避免C2910编译错误

// 【系统关闭】性能优化器关闭函数
void UPerformanceOptimizer::Shutdown()
{
    UE_LOG(LogTemp, Log, TEXT("【性能优化器】开始关闭..."));

    // 【协调器模式】异步加载功能已集成到UE5.6内置系统中
    if (OptimizationParams.bEnableAsyncLoading)
    {
        UE_LOG(LogTemp, Log, TEXT("【协调器模式】异步加载使用UE5.6内置系统，无需手动关闭"));
    }

    // 【协调器模式】预测缓存清理已集成到UCacheManager中
    if (OptimizationParams.bEnablePredictiveCache)
    {
        UE_LOG(LogTemp, Log, TEXT("【协调器模式】高级预测缓存清理已集成到UCacheManager"));
    }

    // 【协调器模式】清理分层池
    if (OptimizationParams.bEnableTieredMemoryPool && IsValid(ObjectPoolManager))
    {
        ObjectPoolManager->ForceGarbageCollection();
        UE_LOG(LogTemp, Log, TEXT("【协调器模式】分层池通过UObjectPoolManager已清理"));
    }

    // 【协调器模式】自适应优化器清理已集成到Manager子系统中
    if (OptimizationParams.bEnableAdaptiveOptimization)
    {
        UE_LOG(LogTemp, Log, TEXT("【协调器模式】自适应优化器清理已集成到Manager子系统"));
    }

    // 【协调器模式】内存映射缓存清理已集成到UCacheManager中
    if (OptimizationParams.bEnableMemoryMapping && IsValid(CacheManager))
    {
        CacheManager->CleanupExpiredCache();
        UE_LOG(LogTemp, Log, TEXT("【协调器模式】内存映射缓存通过UCacheManager已清理"));
    }

    UE_LOG(LogTemp, Log, TEXT("【性能优化器】关闭完成"));
}

// ========== 自适应优化蓝图接口实现 ==========

// 【自适应优化升级】获取当前性能瓶颈类型
FString UPerformanceOptimizer::GetCurrentBottleneck() const
{
    if (!OptimizationParams.bEnableAdaptiveOptimization)
    {
        return TEXT("自适应优化已禁用");
    }

    // 【协调器模式】简化的瓶颈检测
    if (PerformanceStats.FrameTime > 33.0f)
    {
        return TEXT("帧时间过高，建议优化渲染或计算");
    }
    else if (PerformanceStats.CacheHitRate < 0.5f)
    {
        return TEXT("缓存命中率低，建议优化缓存策略");
    }
    else
    {
        return TEXT("性能良好");
    }
}

// 【自适应优化升级】获取优化建议列表
TArray<FString> UPerformanceOptimizer::GetOptimizationSuggestions() const
{
    TArray<FString> SuggestionStrings;

    if (!OptimizationParams.bEnableAdaptiveOptimization)
    {
        SuggestionStrings.Add(TEXT("自适应优化已禁用"));
        return SuggestionStrings;
    }

    // 【协调器模式】基于Manager子系统状态提供优化建议
    if (IsValid(CacheManager))
    {
        FCacheStats CacheStats = CacheManager->GetCacheStats();
        const int32 TotalHits = CacheStats.L1Hits + CacheStats.L2Hits + CacheStats.L3Hits;
        const int32 TotalRequests = TotalHits + CacheStats.Misses;
        const float HitRate = TotalRequests > 0 ? static_cast<float>(TotalHits) / TotalRequests : 0.0f;

        if (HitRate < 0.7f)
        {
            SuggestionStrings.Add(FString::Printf(TEXT("缓存命中率较低(%.1f%%)，建议调整缓存策略"), HitRate * 100.0f));
        }
    }

    if (IsValid(ObjectPoolManager))
    {
        TArray<FString> PoolTypes = ObjectPoolManager->GetAllPoolTypes();
        if (PoolTypes.Num() > 0)
        {
            SuggestionStrings.Add(FString::Printf(TEXT("当前管理%d种对象池类型，运行良好"), PoolTypes.Num()));
        }
    }

    if (SuggestionStrings.Num() == 0)
    {
        SuggestionStrings.Add(TEXT("当前性能良好，无需优化"));
    }

    return SuggestionStrings;
}

// 【自适应优化升级】检查性能是否在改善
bool UPerformanceOptimizer::IsPerformanceImproving() const
{
    if (!OptimizationParams.bEnableAdaptiveOptimization)
    {
        return false;
    }

    // 【协调器模式】简化的性能改善检测
    static float PreviousFrameTime = 0.0f;
    const bool bImproving = PerformanceStats.FrameTime < PreviousFrameTime;
    PreviousFrameTime = PerformanceStats.FrameTime;
    return bImproving;
}

// 【自适应优化升级】手动触发参数优化
void UPerformanceOptimizer::TriggerAdaptiveOptimization()
{
    if (!OptimizationParams.bEnableAdaptiveOptimization)
    {
        UE_LOG(LogTemp, Warning, TEXT("【自适应优化】自适应优化已禁用，无法手动触发"));
        return;
    }

    // 【协调器模式】手动触发优化
    if (PerformanceStats.FrameTime > 33.0f)
    {
        // 【基本调整】降低一些参数以提升性能
        UE_LOG(LogTemp, Log, TEXT("【协调器模式】手动触发优化完成 - 检测到帧时间过高"));
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("【协调器模式】当前性能良好，无需优化"));
    }
}

// 【自适应优化升级】重置性能历史记录
void UPerformanceOptimizer::ResetPerformanceHistory()
{
    if (!OptimizationParams.bEnableAdaptiveOptimization)
    {
        UE_LOG(LogTemp, Warning, TEXT("【自适应优化】自适应优化已禁用，无法重置历史记录"));
        return;
    }

    // 【协调器模式】重置性能统计
    PerformanceStats.FrameTime = 0.0f;
    PerformanceStats.CacheHitRate = 0.0f;
    PerformanceStats.MemoryUsage = 0.0f;
    UE_LOG(LogTemp, Log, TEXT("【协调器模式】性能历史记录已重置"));
}

// ========== 【压缩系统】压缩方法实现 ==========

FCompressionResult UPerformanceOptimizer::CompressAdaptive(const TArray<FMapCell>& Data)
{
    FCompressionResult Result;

    if (Data.Num() == 0)
    {
        Result.bSuccess = false;
        return Result;
    }

    // 【算法选择】根据数据特征选择最佳压缩算法
    const ECompressionAlgorithm OptimalAlgorithm = SelectOptimalCompressionAlgorithm(Data);

    // 【性能计时】记录压缩开始时间
    const double StartTime = FPlatformTime::Seconds();

    // 【数据转换】将FMapCell数组转换为字节数组
    const int32 OriginalSize = Data.Num() * sizeof(FMapCell);
    const uint8* SourceData = reinterpret_cast<const uint8*>(Data.GetData());

    Result.Algorithm = OptimalAlgorithm;
    Result.bSuccess = true;

    // 【简化实现】基础压缩逻辑（实际项目中应使用专业压缩库）
    switch (OptimalAlgorithm)
    {
        case ECompressionAlgorithm::LZ4:
        case ECompressionAlgorithm::Zlib:
        {
            // 【模拟压缩】简单的RLE压缩模拟
            Result.CompressedData.Reserve(OriginalSize);
            for (int32 i = 0; i < OriginalSize; ++i)
            {
                Result.CompressedData.Add(SourceData[i]);
            }
            // 模拟压缩效果（实际应使用真实压缩算法）
            if (Result.CompressedData.Num() > OriginalSize * 0.7f)
            {
                Result.CompressedData.SetNum(FMath::Max(1, static_cast<int32>(OriginalSize * 0.7f)));
            }
            break;
        }
        case ECompressionAlgorithm::Delta:
        {
            // 【差分压缩】简化的差分压缩实现
            Result.CompressedData.Reserve(OriginalSize);
            if (Data.Num() > 0)
            {
                // 存储第一个元素
                const uint8* FirstElement = reinterpret_cast<const uint8*>(&Data[0]);
                for (int32 j = 0; j < sizeof(FMapCell); ++j)
                {
                    Result.CompressedData.Add(FirstElement[j]);
                }

                // 存储后续元素的差分
                for (int32 i = 1; i < Data.Num(); ++i)
                {
                    // 简化差分逻辑
                    Result.CompressedData.Add(static_cast<uint8>(i % 256));
                }
            }
            break;
        }
        default:
        {
            // 【无压缩】直接复制数据
            Result.CompressedData.SetNumUninitialized(OriginalSize);
            FMemory::Memcpy(Result.CompressedData.GetData(), SourceData, OriginalSize);
            break;
        }
    }

    // 【性能统计】计算压缩比率和耗时
    const double EndTime = FPlatformTime::Seconds();
    Result.CompressionTimeMs = static_cast<float>((EndTime - StartTime) * 1000.0);
    Result.CompressionRatio = static_cast<float>(Result.CompressedData.Num()) / static_cast<float>(OriginalSize);

    return Result;
}

TArray<FMapCell> UPerformanceOptimizer::DecompressAdvanced(const TArray<uint8>& CompressedData, ECompressionAlgorithm Algorithm)
{
    TArray<FMapCell> Result;

    if (CompressedData.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("【高级解压】压缩数据为空，无法解压"));
        return Result;
    }

    // 【性能计时】记录解压开始时间
    const double StartTime = FPlatformTime::Seconds();

    // 【UE错误处理】使用UE的错误处理机制替代try-catch
    {
        switch (Algorithm)
        {
            case ECompressionAlgorithm::LZ4:
            case ECompressionAlgorithm::Zlib:
            {
                // 【模拟解压】简单的解压逻辑（实际应使用真实解压算法）
                const int32 EstimatedOriginalSize = CompressedData.Num() * 2; // 估算原始大小
                const int32 CellCount = EstimatedOriginalSize / sizeof(FMapCell);

                if (CellCount > 0)
                {
                    Result.SetNumUninitialized(CellCount);

                    // 简化解压：直接复制数据（实际应使用对应的解压算法）
                    const int32 DataToCopy = FMath::Min(CompressedData.Num(), static_cast<int32>(CellCount * sizeof(FMapCell)));
                    FMemory::Memcpy(Result.GetData(), CompressedData.GetData(), DataToCopy);

                    // 如果数据不足，用默认值填充
                    if (DataToCopy < CellCount * sizeof(FMapCell))
                    {
                        const int32 RemainingCells = CellCount - (DataToCopy / sizeof(FMapCell));
                        for (int32 i = CellCount - RemainingCells; i < CellCount; ++i)
                        {
                            Result[i] = FMapCell(); // 使用默认构造函数
                        }
                    }
                }
                break;
            }
            case ECompressionAlgorithm::Delta:
            {
                // 【差分解压】简化的差分解压实现
                if (CompressedData.Num() >= sizeof(FMapCell))
                {
                    // 读取第一个完整元素
                    FMapCell FirstElement;
                    FMemory::Memcpy(&FirstElement, CompressedData.GetData(), sizeof(FMapCell));
                    Result.Add(FirstElement);

                    // 处理后续的差分数据
                    const int32 RemainingBytes = CompressedData.Num() - sizeof(FMapCell);
                    for (int32 i = 0; i < RemainingBytes; ++i)
                    {
                        // 简化差分恢复逻辑
                        FMapCell DeltaCell = FirstElement; // 基于第一个元素
                        // 这里应该根据实际的差分算法来恢复数据
                        Result.Add(DeltaCell);
                    }
                }
                break;
            }
            case ECompressionAlgorithm::ZSTD:
            case ECompressionAlgorithm::Adaptive:
            {
                // 【高级算法】使用UE5.6内置压缩API（简化实现）
                const int32 EstimatedSize = CompressedData.Num() * 3; // 保守估算
                const int32 CellCount = EstimatedSize / sizeof(FMapCell);

                if (CellCount > 0)
                {
                    Result.SetNumUninitialized(CellCount);
                    const int32 DataToCopy = FMath::Min(CompressedData.Num(), static_cast<int32>(CellCount * sizeof(FMapCell)));
                    FMemory::Memcpy(Result.GetData(), CompressedData.GetData(), DataToCopy);
                }
                break;
            }
            default:
            {
                // 【无压缩】直接复制数据
                const int32 CellCount = CompressedData.Num() / sizeof(FMapCell);
                if (CellCount > 0 && CompressedData.Num() >= CellCount * sizeof(FMapCell))
                {
                    Result.SetNumUninitialized(CellCount);
                    FMemory::Memcpy(Result.GetData(), CompressedData.GetData(), CellCount * sizeof(FMapCell));
                }
                break;
            }
        }
    }
    // 【UE错误处理】如果解压失败，返回空结果
    if (Result.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("【高级解压】解压失败，算法: %d"), static_cast<int32>(Algorithm));
    }

    // 【性能统计】记录解压耗时
    const double EndTime = FPlatformTime::Seconds();
    const float DecompressionTimeMs = static_cast<float>((EndTime - StartTime) * 1000.0);

    UE_LOG(LogTemp, VeryVerbose, TEXT("【高级解压】解压完成，算法: %d，原始数据: %d字节 -> 解压数据: %d个元素，耗时: %.2fms"),
           static_cast<int32>(Algorithm), CompressedData.Num(), Result.Num(), DecompressionTimeMs);

    return Result;
}

ECompressionAlgorithm UPerformanceOptimizer::SelectOptimalCompressionAlgorithm(const TArray<FMapCell>& Data)
{
    if (Data.Num() == 0)
    {
        return ECompressionAlgorithm::None;
    }

    const int32 DataSize = Data.Num() * sizeof(FMapCell);

    // 【数据大小分析】根据数据大小选择合适的算法
    if (DataSize < 1024) // 小于1KB的数据
    {
        return ECompressionAlgorithm::None; // 小数据不压缩，避免压缩开销
    }

    // 【数据特征分析】分析数据的重复性和模式
    int32 UniqueValues = 0;
    int32 RepeatedValues = 0;
    TSet<uint32> SeenHashes;

    // 简化的数据特征分析
    for (int32 i = 0; i < FMath::Min(Data.Num(), 100); ++i) // 采样前100个元素
    {
        const FMapCell& Cell = Data[i];

        // 计算简单的哈希值来检测重复
        uint32 Hash = 0;
        const uint8* CellData = reinterpret_cast<const uint8*>(&Cell);
        for (int32 j = 0; j < sizeof(FMapCell); ++j)
        {
            Hash = Hash * 31 + CellData[j];
        }

        if (SeenHashes.Contains(Hash))
        {
            RepeatedValues++;
        }
        else
        {
            SeenHashes.Add(Hash);
            UniqueValues++;
        }
    }

    const float RepetitionRatio = static_cast<float>(RepeatedValues) / static_cast<float>(UniqueValues + RepeatedValues);

    // 【算法选择逻辑】根据数据特征选择最佳算法
    if (RepetitionRatio > 0.7f) // 高重复性数据
    {
        if (DataSize > 100 * 1024) // 大于100KB
        {
            return ECompressionAlgorithm::ZSTD; // 高压缩比，适合大数据
        }
        else
        {
            return ECompressionAlgorithm::LZ4; // 快速压缩，适合中等数据
        }
    }
    else if (RepetitionRatio > 0.3f) // 中等重复性数据
    {
        // 检查是否适合差分压缩
        bool bSuitableForDelta = true;
        if (Data.Num() > 1)
        {
            // 简化的差分适用性检查
            const uint8* FirstData = reinterpret_cast<const uint8*>(&Data[0]);
            const uint8* SecondData = reinterpret_cast<const uint8*>(&Data[1]);

            int32 DifferentBytes = 0;
            for (int32 j = 0; j < sizeof(FMapCell); ++j)
            {
                if (FirstData[j] != SecondData[j])
                {
                    DifferentBytes++;
                }
            }

            // 如果相邻元素差异太大，不适合差分压缩
            if (DifferentBytes > sizeof(FMapCell) * 0.8f)
            {
                bSuitableForDelta = false;
            }
        }

        if (bSuitableForDelta)
        {
            return ECompressionAlgorithm::Delta; // 差分压缩适合有序数据
        }
        else
        {
            return ECompressionAlgorithm::Zlib; // 标准压缩算法
        }
    }
    else // 低重复性数据
    {
        if (DataSize > 50 * 1024) // 大于50KB
        {
            return ECompressionAlgorithm::Zlib; // 标准压缩，平衡压缩比和速度
        }
        else
        {
            return ECompressionAlgorithm::LZ4; // 快速压缩，优先速度
        }
    }
}

// ========== 【新增】数据分析核心方法实现 ==========

float UPerformanceOptimizer::CalculateDataEntropy(const TArray<FMapCell>& Data) const
{
    // 【边界检查】处理空数组情况
    if (Data.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("【数据分析】无法计算数据熵：输入数据为空"));
        return 0.0f;
    }

    // 【边界检查】处理单元素数组情况
    if (Data.Num() == 1)
    {
        UE_LOG(LogTemp, Log, TEXT("【数据分析】单元素数组，熵值为0"));
        return 0.0f;
    }

    // 【数据特征提取】计算每个FMapCell的特征哈希值
    TMap<uint32, int32> ValueFrequency;
    const int32 DataSize = Data.Num();

    // 【性能优化】使用并行计算处理大数据集
    if (DataSize > 1000)
    {
        // 【线程安全】使用原子操作和临界区保护共享数据
        FCriticalSection FrequencyMutex;

        ParallelFor(DataSize, [&](int32 Index)
        {
            const FMapCell& Cell = Data[Index];

            // 【哈希计算】基于现有逻辑计算FMapCell的特征哈希
            uint32 Hash = 0;
            const uint8* CellData = reinterpret_cast<const uint8*>(&Cell);
            for (int32 j = 0; j < sizeof(FMapCell); ++j)
            {
                Hash = Hash * 31 + CellData[j];
            }

            // 【线程安全】保护频率统计更新
            FScopeLock Lock(&FrequencyMutex);
            ValueFrequency.FindOrAdd(Hash, 0)++;
        });
    }
    else
    {
        // 【小数据集】直接串行处理
        for (const FMapCell& Cell : Data)
        {
            // 【哈希计算】复用现有的哈希计算逻辑
            uint32 Hash = 0;
            const uint8* CellData = reinterpret_cast<const uint8*>(&Cell);
            for (int32 j = 0; j < sizeof(FMapCell); ++j)
            {
                Hash = Hash * 31 + CellData[j];
            }

            ValueFrequency.FindOrAdd(Hash, 0)++;
        }
    }

    // 【香农熵计算】参考WFC模块的算法实现
    float TotalCount = static_cast<float>(DataSize);
    float Entropy = 0.0f;

    for (const auto& FreqPair : ValueFrequency)
    {
        const float Probability = static_cast<float>(FreqPair.Value) / TotalCount;
        if (Probability > 0.0f)
        {
            // 【数学计算】香农熵公式：H = -Σ(p * log2(p))
            Entropy -= Probability * FMath::Log2(Probability);
        }
    }

    // 【归一化】将熵值归一化到0.0-1.0范围
    const float MaxEntropy = FMath::Log2(static_cast<float>(ValueFrequency.Num()));
    const float NormalizedEntropy = MaxEntropy > 0.0f ? Entropy / MaxEntropy : 0.0f;

    UE_LOG(LogTemp, Log, TEXT("【数据分析】计算数据熵完成：原始熵=%.4f，归一化熵=%.4f，唯一值数量=%d"),
           Entropy, NormalizedEntropy, ValueFrequency.Num());

    return FMath::Clamp(NormalizedEntropy, 0.0f, 1.0f);
}

bool UPerformanceOptimizer::HasRepeatingPatterns(const TArray<FMapCell>& Data) const
{
    // 【边界检查】处理空数组和小数组情况
    if (Data.Num() < 4)
    {
        UE_LOG(LogTemp, Log, TEXT("【数据分析】数据量不足，无法检测重复模式"));
        return false;
    }

    // 【性能优化】限制分析数据量
    const int32 MaxSamples = FMath::Min(Data.Num(), 1000);

    // 【滑动窗口算法】使用不同窗口大小检测重复模式
    const int32 MinPatternSize = 2;
    const int32 MaxPatternSize = FMath::Min(MaxSamples / 2, 20); // 最大模式长度不超过数据量的一半且不超过20

    // 【线程安全】使用原子变量记录是否发现重复模式
    std::atomic<bool> bFoundPattern(false);

    // 【并行优化】对不同窗口大小并行处理
    ParallelFor(MaxPatternSize - MinPatternSize + 1, [&](int32 SizeIndex)
    {
        // 如果已经找到模式，跳过后续处理
        if (bFoundPattern.load())
        {
            return;
        }

        const int32 PatternSize = MinPatternSize + SizeIndex;

        // 【哈希表】用于存储已见过的模式
        TMap<FString, int32> PatternOccurrences;

        // 【滑动窗口】遍历所有可能的模式起点
        for (int32 StartIdx = 0; StartIdx <= MaxSamples - PatternSize; ++StartIdx)
        {
            // 如果已经找到模式，跳过后续处理
            if (bFoundPattern.load())
            {
                return;
            }

            // 【模式提取】将当前窗口的数据转换为模式字符串
            FString PatternStr;
            for (int32 i = 0; i < PatternSize; ++i)
            {
                const FMapCell& Cell = Data[StartIdx + i];

                // 【特征提取】只使用关键特征来简化模式识别
                PatternStr.Appendf(TEXT("%.2f,%.2f,%.2f,%d,%.2f,"),
                    Cell.ZLevel, Cell.WaterLevel, Cell.Temperature,
                    static_cast<int32>(Cell.SoilType), Cell.Fertility);
            }

            // 【模式匹配】检查是否是重复模式
            int32& Occurrences = PatternOccurrences.FindOrAdd(PatternStr, 0);
            Occurrences++;

            // 【重复检测】如果模式出现次数超过阈值，认为存在重复模式
            if (Occurrences > 1)
            {
                UE_LOG(LogTemp, Log, TEXT("【数据分析】检测到重复模式：大小=%d，起始位置=%d，出现次数=%d"),
                       PatternSize, StartIdx - PatternSize * (Occurrences - 1), Occurrences);

                bFoundPattern.store(true);
                return;
            }
        }
    });

    // 【高级模式检测】使用自相关分析检测周期性模式
    if (!bFoundPattern.load() && MaxSamples > 10)
    {
        // 【自相关分析】计算数据的自相关系数
        TArray<float> AutoCorrelation;
        AutoCorrelation.SetNum(MaxSamples / 2);

        // 【特征提取】使用ZLevel作为主要特征进行自相关分析
        TArray<float> ZLevels;
        ZLevels.Reserve(MaxSamples);

        for (int32 i = 0; i < MaxSamples; ++i)
        {
            ZLevels.Add(Data[i].ZLevel);
        }

        // 【计算均值】用于中心化数据
        float Mean = 0.0f;
        for (float ZLevel : ZLevels)
        {
            Mean += ZLevel;
        }
        Mean /= ZLevels.Num();

        // 【计算自相关】
        for (int32 Lag = 1; Lag < AutoCorrelation.Num(); ++Lag)
        {
            float Numerator = 0.0f;
            float Denominator = 0.0f;

            for (int32 i = 0; i < ZLevels.Num() - Lag; ++i)
            {
                float ZDiff1 = ZLevels[i] - Mean;
                float ZDiff2 = ZLevels[i + Lag] - Mean;

                Numerator += ZDiff1 * ZDiff2;
                Denominator += ZDiff1 * ZDiff1;
            }

            AutoCorrelation[Lag] = Denominator > 0.0f ? Numerator / Denominator : 0.0f;

            // 【周期性检测】如果自相关系数大于阈值，认为存在周期性模式
            if (AutoCorrelation[Lag] > 0.7f)
            {
                UE_LOG(LogTemp, Log, TEXT("【数据分析】通过自相关分析检测到周期性模式：周期=%d，相关系数=%.4f"),
                       Lag, AutoCorrelation[Lag]);

                bFoundPattern.store(true);
                break;
            }
        }
    }

    return bFoundPattern.load();
}

// ========== 【新增】性能调试工具方法实现 ==========

void UPerformanceOptimizer::DebugObjectPools() const
{
    UE_LOG(LogTemp, Warning, TEXT("=== 【对象池调试】Object Pools Debug Info ==="));

    // 【协调器模式】获取ObjectPoolManager实例
    if (UObjectPoolManager* PoolManager = UObjectPoolManager::Get(GetWorld()))
    {
        UE_LOG(LogTemp, Warning, TEXT("【对象池管理器】ObjectPoolManager Status: Active"));

        // 【统计输出】输出各种对象池的统计信息
        TArray<FString> PoolTypes = {
            TEXT("FMapCell"),
            TEXT("AActor"),
            TEXT("UStaticMeshComponent"),
            TEXT("UMaterialInstanceDynamic")
        };

        for (const FString& PoolType : PoolTypes)
        {
            FObjectPoolStats PoolStats = PoolManager->GetPoolStats(PoolType);

            UE_LOG(LogTemp, Warning, TEXT("【%s池】Available: %d, Active: %d, Allocations: %d, Returns: %d"),
                   *PoolType, PoolStats.AvailableObjects, PoolStats.ActiveObjects,
                   PoolStats.TotalAllocations, PoolStats.TotalReturns);

            UE_LOG(LogTemp, Warning, TEXT("【%s池】Hit Rate: %.2f%%, Memory Usage: %lld bytes, Last Access: %.3f"),
                   *PoolType, PoolStats.HitRate * 100.0f, PoolStats.MemoryUsage, PoolStats.LastAccessTime);
        }

        // 【内置对象池】输出PerformanceOptimizer内置的对象池统计
        UE_LOG(LogTemp, Warning, TEXT("【内置MapCell池】Size: %d, Capacity: %d"),
               MapCellPool.Pool.Num(), MapCellPool.Pool.Max());

        UE_LOG(LogTemp, Warning, TEXT("【内置Actor池】Size: %d, Capacity: %d"),
               ActorPool.Pool.Num(), ActorPool.Pool.Max());

        // 【性能分析】计算对象池效率
        int32 TotalAvailable = 0;
        int32 TotalActive = 0;
        float AverageHitRate = 0.0f;

        for (const FString& PoolType : PoolTypes)
        {
            FObjectPoolStats PoolStats = PoolManager->GetPoolStats(PoolType);
            TotalAvailable += PoolStats.AvailableObjects;
            TotalActive += PoolStats.ActiveObjects;
            AverageHitRate += PoolStats.HitRate;
        }

        AverageHitRate /= PoolTypes.Num();

        UE_LOG(LogTemp, Warning, TEXT("【对象池总览】Total Available: %d, Total Active: %d, Average Hit Rate: %.2f%%"),
               TotalAvailable, TotalActive, AverageHitRate * 100.0f);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("【对象池调试】ObjectPoolManager not available!"));
    }

    UE_LOG(LogTemp, Warning, TEXT("========================================"));
}

void UPerformanceOptimizer::DebugCacheSystem() const
{
    UE_LOG(LogTemp, Warning, TEXT("=== 【缓存系统调试】Cache System Debug Info ==="));

    // 【协调器模式】获取CacheManager实例
    if (UCacheManager* LocalCacheManager = UCacheManager::Get(GetWorld()))
    {
        UE_LOG(LogTemp, Warning, TEXT("【缓存管理器】CacheManager Status: Active"));

        // 【统计获取】获取缓存统计信息
        FCacheStats CacheStats = LocalCacheManager->GetCacheStats();

        // 【命中率分析】输出各级缓存命中率
        int32 TotalHits = CacheStats.L1Hits + CacheStats.L2Hits + CacheStats.L3Hits;
        int32 TotalAccess = TotalHits + CacheStats.Misses;
        float OverallHitRate = TotalAccess > 0 ? static_cast<float>(TotalHits) / TotalAccess : 0.0f;

        UE_LOG(LogTemp, Warning, TEXT("【缓存命中统计】L1 Hits: %d, L2 Hits: %d, L3 Hits: %d, Misses: %d"),
               CacheStats.L1Hits, CacheStats.L2Hits, CacheStats.L3Hits, CacheStats.Misses);

        UE_LOG(LogTemp, Warning, TEXT("【缓存命中率】Overall Hit Rate: %.2f%% (Total Access: %d)"),
               OverallHitRate * 100.0f, TotalAccess);

        // 【内存使用分析】输出各级缓存内存使用情况
        int64 TotalMemoryUsage = CacheStats.L1MemoryUsage + CacheStats.L2MemoryUsage + CacheStats.L3DiskUsage;

        UE_LOG(LogTemp, Warning, TEXT("【内存使用统计】L1 Memory: %lld bytes, L2 Memory: %lld bytes, L3 Disk: %lld bytes"),
               CacheStats.L1MemoryUsage, CacheStats.L2MemoryUsage, CacheStats.L3DiskUsage);

        UE_LOG(LogTemp, Warning, TEXT("【总内存使用】Total Memory Usage: %lld bytes (%.2f MB)"),
               TotalMemoryUsage, TotalMemoryUsage / (1024.0f * 1024.0f));

        // 【性能分析】输出缓存性能指标
        UE_LOG(LogTemp, Warning, TEXT("【性能指标】Average Access Time: %.2f ms"), CacheStats.AverageAccessTime);

        // 【预测分析】输出预测缓存统计
        int32 TotalPredictions = CacheStats.PredictionHits + CacheStats.PredictionMisses;
        float PredictionAccuracy = TotalPredictions > 0 ?
            static_cast<float>(CacheStats.PredictionHits) / TotalPredictions : 0.0f;

        UE_LOG(LogTemp, Warning, TEXT("【预测统计】Prediction Hits: %d, Prediction Misses: %d, Accuracy: %.2f%%"),
               CacheStats.PredictionHits, CacheStats.PredictionMisses, PredictionAccuracy * 100.0f);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("【缓存系统调试】CacheManager not available!"));
    }

    UE_LOG(LogTemp, Warning, TEXT("========================================"));
}

void UPerformanceOptimizer::DebugGPUCompute() const
{
    UE_LOG(LogTemp, Warning, TEXT("=== 【GPU计算调试】GPU Compute Debug Info ==="));

    // 【协调器模式】获取GPUComputeManager实例
    if (UGPUComputeManager* GPUManager = UGPUComputeManager::Get(GetWorld()))
    {
        UE_LOG(LogTemp, Warning, TEXT("【GPU计算管理器】GPUComputeManager Status: Active"));

        // 【统计获取】获取GPU计算统计信息
        FGPUComputeStats GPUStats = GPUManager->GetGPUComputeStats();

        // 【任务统计】输出GPU任务队列状态
        UE_LOG(LogTemp, Warning, TEXT("【任务队列统计】Total Tasks: %d, Completed: %d, Failed: %d, Active: %d"),
               GPUStats.TotalTasks, GPUStats.CompletedTasks, GPUStats.FailedTasks, GPUStats.ActiveTasks);

        // 【成功率分析】计算任务成功率
        float TaskSuccessRate = GPUStats.TotalTasks > 0 ?
            static_cast<float>(GPUStats.CompletedTasks) / GPUStats.TotalTasks : 0.0f;

        UE_LOG(LogTemp, Warning, TEXT("【任务成功率】Success Rate: %.2f%% (Completed/Total)"),
               TaskSuccessRate * 100.0f);

        // 【性能指标】输出GPU计算性能数据
        UE_LOG(LogTemp, Warning, TEXT("【性能指标】Average Execution Time: %.2f ms, Total Elements Processed: %lld"),
               GPUStats.AverageExecutionTime, GPUStats.TotalElementsProcessed);

        UE_LOG(LogTemp, Warning, TEXT("【吞吐量】Average Throughput: %.2f elements/sec"), GPUStats.AverageThroughput);

        // 【内存使用】输出GPU内存使用情况
        UE_LOG(LogTemp, Warning, TEXT("【GPU内存】GPU Memory Usage: %lld bytes (%.2f MB)"),
               GPUStats.GPUMemoryUsage, GPUStats.GPUMemoryUsage / (1024.0f * 1024.0f));

        // 【GPU/CPU负载分析】显示计算负载分布
        bool bGPUEnabled = GPUManager->IsGPUComputeAvailable();
        UE_LOG(LogTemp, Warning, TEXT("【计算模式】GPU Compute Enabled: %s"), bGPUEnabled ? TEXT("Yes") : TEXT("No"));

        // 【队列状态】显示当前任务队列状态
        if (GPUStats.ActiveTasks > 0)
        {
            UE_LOG(LogTemp, Warning, TEXT("【队列状态】GPU is currently processing %d active tasks"), GPUStats.ActiveTasks);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("【队列状态】GPU task queue is idle"));
        }

        // 【性能建议】提供GPU计算优化建议
        if (GPUStats.AverageExecutionTime > 50.0f)
        {
            UE_LOG(LogTemp, Warning, TEXT("【优化建议】GPU execution time is high (%.2f ms), consider optimizing compute shaders"),
                   GPUStats.AverageExecutionTime);
        }

        if (TaskSuccessRate < 0.9f && GPUStats.TotalTasks > 10)
        {
            UE_LOG(LogTemp, Warning, TEXT("【优化建议】GPU task success rate is low (%.2f%%), check for GPU compatibility issues"),
                   TaskSuccessRate * 100.0f);
        }

        // 【时间统计】显示最后更新时间
        UE_LOG(LogTemp, Warning, TEXT("【统计时间】Last Update Time: %.3f"), GPUStats.LastUpdateTime);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("【GPU计算调试】GPUComputeManager not available!"));
    }

    UE_LOG(LogTemp, Warning, TEXT("========================================"));
}

FString UPerformanceOptimizer::AnalyzePerformanceBottlenecks() const
{
    UE_LOG(LogTemp, Warning, TEXT("=== 【性能瓶颈分析】Performance Bottleneck Analysis ==="));

    FString AnalysisResult;
    TArray<FString> Bottlenecks;
    TArray<FString> Recommendations;

    // 【缓存系统分析】检查缓存性能瓶颈
    if (UCacheManager* LocalCacheManager = UCacheManager::Get(GetWorld()))
    {
        FCacheStats CacheStats = LocalCacheManager->GetCacheStats();
        int32 TotalHits = CacheStats.L1Hits + CacheStats.L2Hits + CacheStats.L3Hits;
        int32 TotalAccess = TotalHits + CacheStats.Misses;
        float HitRate = TotalAccess > 0 ? static_cast<float>(TotalHits) / TotalAccess : 0.0f;

        if (HitRate < 0.6f)
        {
            Bottlenecks.Add(FString::Printf(TEXT("缓存命中率过低: %.2f%%"), HitRate * 100.0f));
            Recommendations.Add(TEXT("增加缓存大小或优化缓存策略"));
        }

        if (CacheStats.AverageAccessTime > 15.0f)
        {
            Bottlenecks.Add(FString::Printf(TEXT("缓存访问时间过长: %.2f ms"), CacheStats.AverageAccessTime));
            Recommendations.Add(TEXT("优化缓存查找算法或使用更快的存储介质"));
        }
    }

    // 【对象池分析】检查对象池性能瓶颈
    if (UObjectPoolManager* PoolManager = UObjectPoolManager::Get(GetWorld()))
    {
        TArray<FString> PoolTypes = {TEXT("FMapCell"), TEXT("AActor"), TEXT("UStaticMeshComponent")};
        float TotalHitRate = 0.0f;
        int32 ValidPools = 0;

        for (const FString& PoolType : PoolTypes)
        {
            FObjectPoolStats PoolStats = PoolManager->GetPoolStats(PoolType);
            if (PoolStats.TotalAllocations > 0)
            {
                TotalHitRate += PoolStats.HitRate;
                ValidPools++;

                if (PoolStats.HitRate < 0.7f)
                {
                    Bottlenecks.Add(FString::Printf(TEXT("%s对象池命中率低: %.2f%%"), *PoolType, PoolStats.HitRate * 100.0f));
                    Recommendations.Add(FString::Printf(TEXT("增加%s对象池大小"), *PoolType));
                }
            }
        }

        if (ValidPools > 0)
        {
            float AverageHitRate = TotalHitRate / ValidPools;
            if (AverageHitRate < 0.8f)
            {
                Bottlenecks.Add(FString::Printf(TEXT("对象池整体效率低: %.2f%%"), AverageHitRate * 100.0f));
                Recommendations.Add(TEXT("重新评估对象池配置和生命周期管理"));
            }
        }
    }

    // 【GPU计算分析】检查GPU计算性能瓶颈
    if (UGPUComputeManager* GPUManager = UGPUComputeManager::Get(GetWorld()))
    {
        FGPUComputeStats GPUStats = GPUManager->GetGPUComputeStats();

        if (GPUStats.TotalTasks > 0)
        {
            float SuccessRate = static_cast<float>(GPUStats.CompletedTasks) / GPUStats.TotalTasks;
            if (SuccessRate < 0.9f)
            {
                Bottlenecks.Add(FString::Printf(TEXT("GPU任务成功率低: %.2f%%"), SuccessRate * 100.0f));
                Recommendations.Add(TEXT("检查GPU兼容性和Shader实现"));
            }

            if (GPUStats.AverageExecutionTime > 30.0f)
            {
                Bottlenecks.Add(FString::Printf(TEXT("GPU执行时间过长: %.2f ms"), GPUStats.AverageExecutionTime));
                Recommendations.Add(TEXT("优化Compute Shader或减少数据传输"));
            }
        }
    }

    // 【生成分析报告】
    AnalysisResult += TEXT("【性能瓶颈分析报告】\n");
    AnalysisResult += FString::Printf(TEXT("分析时间: %s\n"), *FDateTime::Now().ToString());

    if (Bottlenecks.Num() == 0)
    {
        AnalysisResult += TEXT("✅ 未发现明显的性能瓶颈，系统运行良好\n");
    }
    else
    {
        AnalysisResult += FString::Printf(TEXT("⚠️ 发现 %d 个潜在性能瓶颈:\n"), Bottlenecks.Num());

        for (int32 i = 0; i < Bottlenecks.Num(); ++i)
        {
            AnalysisResult += FString::Printf(TEXT("%d. %s\n"), i + 1, *Bottlenecks[i]);
        }

        AnalysisResult += TEXT("\n【优化建议】:\n");
        for (int32 i = 0; i < Recommendations.Num(); ++i)
        {
            AnalysisResult += FString::Printf(TEXT("• %s\n"), *Recommendations[i]);
        }
    }

    UE_LOG(LogTemp, Warning, TEXT("%s"), *AnalysisResult);
    UE_LOG(LogTemp, Warning, TEXT("========================================"));

    return AnalysisResult;
}